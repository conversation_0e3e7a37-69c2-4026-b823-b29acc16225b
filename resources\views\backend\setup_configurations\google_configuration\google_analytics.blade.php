@extends('backend.layouts.app')

@section('title', 'Google Analytics Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Google Analytics Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('business_settings.update') }}" method="POST">
                        @csrf
                        <input type="hidden" name="types[]" value="google_analytics_status">
                        <input type="hidden" name="types[]" value="google_analytics_tracking_id">
                        <input type="hidden" name="types[]" value="google_analytics_measurement_id">

                        <div class="form-group">
                            <label class="form-label">Enable Google Analytics</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="google_analytics_status" 
                                    {{ get_setting('google_analytics_status') == 1 ? 'checked' : '' }}>
                                <label class="form-check-label">Enable Google Analytics tracking</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="google_analytics_tracking_id">Google Analytics Tracking ID (UA-XXXXXXXXX-X)</label>
                            <input type="text" name="google_analytics_tracking_id" class="form-control" 
                                value="{{ get_setting('google_analytics_tracking_id') }}" 
                                placeholder="UA-XXXXXXXXX-X">
                            <small class="form-text text-muted">
                                Universal Analytics Tracking ID (Legacy format)
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="google_analytics_measurement_id">Google Analytics 4 Measurement ID (G-XXXXXXXXXX)</label>
                            <input type="text" name="google_analytics_measurement_id" class="form-control" 
                                value="{{ get_setting('google_analytics_measurement_id') }}" 
                                placeholder="G-XXXXXXXXXX">
                            <small class="form-text text-muted">
                                Google Analytics 4 Measurement ID (Recommended)
                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Google Analytics Settings</button>
                        </div>
                    </form>

                    <!-- Setup Instructions -->
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Setup Instructions</h5>
                            <ol>
                                <li>Go to <a href="https://analytics.google.com/" target="_blank">Google Analytics</a></li>
                                <li>Create a new property or select an existing one</li>
                                <li>For GA4: Copy the Measurement ID (G-XXXXXXXXXX)</li>
                                <li>For Universal Analytics: Copy the Tracking ID (UA-XXXXXXXXX-X)</li>
                                <li>Paste the ID(s) in the fields above</li>
                                <li>Enable the tracking and save settings</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Current Status</h5>
                            <div class="alert {{ get_setting('google_analytics_status') == 1 ? 'alert-success' : 'alert-warning' }}">
                                Google Analytics is currently 
                                <strong>{{ get_setting('google_analytics_status') == 1 ? 'Enabled' : 'Disabled' }}</strong>
                            </div>
                            
                            @if(get_setting('google_analytics_measurement_id'))
                                <div class="alert alert-info">
                                    <strong>GA4 Configured:</strong><br>
                                    Measurement ID: {{ get_setting('google_analytics_measurement_id') }}
                                </div>
                            @endif
                            
                            @if(get_setting('google_analytics_tracking_id'))
                                <div class="alert alert-info">
                                    <strong>Universal Analytics Configured:</strong><br>
                                    Tracking ID: {{ get_setting('google_analytics_tracking_id') }}
                                </div>
                            @endif
                            
                            @if(!get_setting('google_analytics_measurement_id') && !get_setting('google_analytics_tracking_id'))
                                <div class="alert alert-warning">
                                    <strong>No Analytics ID Configured</strong><br>
                                    Please add at least one Analytics ID to enable tracking.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Features Information -->
                    <hr>
                    <h5>Analytics Features</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                    <h6>Traffic Analysis</h6>
                                    <p class="text-muted">Track page views, sessions, and user behavior</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-3x text-success mb-3"></i>
                                    <h6>Audience Insights</h6>
                                    <p class="text-muted">Understand your visitors' demographics and interests</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-bullseye fa-3x text-warning mb-3"></i>
                                    <h6>Goal Tracking</h6>
                                    <p class="text-muted">Monitor conversions and important user actions</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
