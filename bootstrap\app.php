<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => \App\Http\Middleware\Role::class,
            'rate.limit' => \App\Http\Middleware\RateLimitMiddleware::class,
            'secure.upload' => \App\Http\Middleware\SecureFileUpload::class,
        ]);

        // Add security headers middleware globally
        $middleware->append(\App\Http\Middleware\SecurityHeaders::class);

        // Add SEO optimization middleware globally
        $middleware->append(\App\Http\Middleware\SeoOptimization::class);

        // Add rate limiting to web routes
        $middleware->web(append: [
            \App\Http\Middleware\RateLimitMiddleware::class . ':web,120,1', // 120 requests per minute
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // Custom error handling for production
        $exceptions->render(function (Throwable $e, $request) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => app()->environment('production') ? 'Server Error' : $e->getMessage(),
                    'error' => app()->environment('production') ? null : [
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                    ]
                ], 500);
            }

            // Handle 404 errors
            if ($e instanceof \Symfony\Component\HttpKernel\Exception\NotFoundHttpException) {
                return response()->view('errors.404', [], 404);
            }

            // Handle 403 errors
            if ($e instanceof \Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException) {
                return response()->view('errors.403', [], 403);
            }

            // Handle 500 errors in production
            if (app()->environment('production')) {
                return response()->view('errors.500', [], 500);
            }
        });
    })->create();
