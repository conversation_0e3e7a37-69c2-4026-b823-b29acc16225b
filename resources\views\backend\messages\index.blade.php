@extends('backend.layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4">
    <h1 class="text-2xl font-bold mb-4">Messages</h1>

    <a href="{{ route('messages.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mb-4 inline-block">
        New Message
    </a>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if($messages->isEmpty())
        <p>No messages found.</p>
    @else
        <table class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b">Subject</th>
                    <th class="py-2 px-4 border-b">Sender</th>
                    <th class="py-2 px-4 border-b">Recipient</th>
                    <th class="py-2 px-4 border-b">Sent At</th>
                    <th class="py-2 px-4 border-b">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach($messages as $message)
                <tr>
                    <td class="py-2 px-4 border-b">{{ $message->subject }}</td>
                    <td class="py-2 px-4 border-b">{{ $message->sender->name ?? 'N/A' }}</td>
                    <td class="py-2 px-4 border-b">{{ $message->recipient->name ?? 'N/A' }}</td>
                    <td class="py-2 px-4 border-b">{{ $message->created_at->format('Y-m-d H:i') }}</td>
                    <td class="py-2 px-4 border-b">
                        <a href="{{ route('messages.show', $message) }}" class="text-blue-600 hover:underline mr-2">View</a>
                        <a href="{{ route('messages.edit', $message) }}" class="text-yellow-600 hover:underline mr-2">Edit</a>
                        <form action="{{ route('messages.destroy', $message) }}" method="POST" class="inline-block" onsubmit="return confirm('Are you sure you want to delete this message?');">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="text-red-600 hover:underline">Delete</button>
                        </form>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    @endif
</div>
@endsection
