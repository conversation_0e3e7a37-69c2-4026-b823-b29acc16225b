<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for job_listings table
        Schema::table('job_listings', function (Blueprint $table) {
            $table->index(['status', 'created_at'], 'idx_job_listings_status_created');
            $table->index(['company_id', 'status'], 'idx_job_listings_company_status');
            $table->index(['job_type', 'status'], 'idx_job_listings_type_status');
            $table->index(['location', 'status'], 'idx_job_listings_location_status');
            $table->index(['salary_min', 'salary_max'], 'idx_job_listings_salary_range');
            $table->index(['experience_level', 'status'], 'idx_job_listings_experience_status');
            $table->index(['remote_option', 'status'], 'idx_job_listings_remote_status');
            $table->index('slug', 'idx_job_listings_slug');
            $table->index('views_count', 'idx_job_listings_views');
        });

        // Add indexes for companies table
        Schema::table('companies', function (Blueprint $table) {
            $table->index('slug', 'idx_companies_slug');
            $table->index(['name', 'created_at'], 'idx_companies_name_created');
        });

        // Add indexes for job_applications table
        Schema::table('job_applications', function (Blueprint $table) {
            $table->index(['user_id', 'status'], 'idx_job_applications_user_status');
            $table->index(['job_listing_id', 'status'], 'idx_job_applications_job_status');
            $table->index(['status', 'created_at'], 'idx_job_applications_status_created');
        });

        // Add indexes for categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->index(['parent_id', 'name'], 'idx_categories_parent_name');
            $table->index('slug', 'idx_categories_slug');
        });

        // Add indexes for category_job_listing table
        Schema::table('category_job_listing', function (Blueprint $table) {
            $table->index('category_id', 'idx_category_job_listing_category');
            $table->index('job_listing_id', 'idx_category_job_listing_job');
        });

        // Add indexes for users table
        Schema::table('users', function (Blueprint $table) {
            $table->index('email', 'idx_users_email');
            $table->index(['created_at', 'email_verified_at'], 'idx_users_created_verified');
        });

        // Add indexes for messages table
        Schema::table('messages', function (Blueprint $table) {
            $table->index(['sender_id', 'created_at'], 'idx_messages_sender_created');
            $table->index(['recipient_id', 'read_at'], 'idx_messages_recipient_read');
            $table->index(['related_job_id', 'created_at'], 'idx_messages_job_created');
        });

        // Add indexes for pages table
        Schema::table('pages', function (Blueprint $table) {
            $table->index(['is_active', 'sort_order'], 'idx_pages_active_sort');
            $table->index('slug', 'idx_pages_slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes for job_listings table
        Schema::table('job_listings', function (Blueprint $table) {
            $table->dropIndex('idx_job_listings_status_created');
            $table->dropIndex('idx_job_listings_company_status');
            $table->dropIndex('idx_job_listings_type_status');
            $table->dropIndex('idx_job_listings_location_status');
            $table->dropIndex('idx_job_listings_salary_range');
            $table->dropIndex('idx_job_listings_experience_status');
            $table->dropIndex('idx_job_listings_remote_status');
            $table->dropIndex('idx_job_listings_slug');
            $table->dropIndex('idx_job_listings_views');
        });

        // Drop indexes for companies table
        Schema::table('companies', function (Blueprint $table) {
            $table->dropIndex('idx_companies_slug');
            $table->dropIndex('idx_companies_name_created');
        });

        // Drop indexes for job_applications table
        Schema::table('job_applications', function (Blueprint $table) {
            $table->dropIndex('idx_job_applications_user_status');
            $table->dropIndex('idx_job_applications_job_status');
            $table->dropIndex('idx_job_applications_status_created');
        });

        // Drop indexes for categories table
        Schema::table('categories', function (Blueprint $table) {
            $table->dropIndex('idx_categories_parent_name');
            $table->dropIndex('idx_categories_slug');
        });

        // Drop indexes for category_job_listing table
        Schema::table('category_job_listing', function (Blueprint $table) {
            $table->dropIndex('idx_category_job_listing_category');
            $table->dropIndex('idx_category_job_listing_job');
        });

        // Drop indexes for users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_email');
            $table->dropIndex('idx_users_created_verified');
        });

        // Drop indexes for messages table
        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex('idx_messages_sender_created');
            $table->dropIndex('idx_messages_recipient_read');
            $table->dropIndex('idx_messages_job_created');
        });

        // Drop indexes for pages table
        Schema::table('pages', function (Blueprint $table) {
            $table->dropIndex('idx_pages_active_sort');
            $table->dropIndex('idx_pages_slug');
        });
    }
};
