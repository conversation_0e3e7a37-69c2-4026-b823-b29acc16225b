@extends('backend.layouts.app')

@section('title', 'Facebook Comments Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Facebook Comments Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('facebook-comment.update') }}" method="POST">
                        @csrf
                        
                        <div class="form-group">
                            <label class="form-label">Enable Facebook Comments</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="facebook_comment_status" 
                                    {{ get_setting('facebook_comment_status') == 1 ? 'checked' : '' }}>
                                <label class="form-check-label">Enable Facebook Comments on job listings</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="facebook_app_id">Facebook App ID</label>
                            <input type="text" name="facebook_app_id" class="form-control" 
                                value="{{ get_setting('facebook_app_id') }}" 
                                placeholder="Enter your Facebook App ID">
                            <small class="form-text text-muted">
                                Required for Facebook Comments. Create an app at <a href="https://developers.facebook.com/" target="_blank">developers.facebook.com</a>
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="facebook_comment_num_posts">Number of Comments to Display</label>
                            <select name="facebook_comment_num_posts" class="form-control">
                                <option value="5" {{ get_setting('facebook_comment_num_posts', '5') == '5' ? 'selected' : '' }}>5 Comments</option>
                                <option value="10" {{ get_setting('facebook_comment_num_posts') == '10' ? 'selected' : '' }}>10 Comments</option>
                                <option value="15" {{ get_setting('facebook_comment_num_posts') == '15' ? 'selected' : '' }}>15 Comments</option>
                                <option value="20" {{ get_setting('facebook_comment_num_posts') == '20' ? 'selected' : '' }}>20 Comments</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="facebook_comment_order_by">Comment Order</label>
                            <select name="facebook_comment_order_by" class="form-control">
                                <option value="social" {{ get_setting('facebook_comment_order_by', 'social') == 'social' ? 'selected' : '' }}>Social Ranking</option>
                                <option value="reverse_time" {{ get_setting('facebook_comment_order_by') == 'reverse_time' ? 'selected' : '' }}>Reverse Time (Newest First)</option>
                                <option value="time" {{ get_setting('facebook_comment_order_by') == 'time' ? 'selected' : '' }}>Time (Oldest First)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="facebook_comment_width">Comment Box Width</label>
                            <input type="number" name="facebook_comment_width" class="form-control" 
                                value="{{ get_setting('facebook_comment_width', '100') }}" 
                                placeholder="100" min="1" max="100">
                            <small class="form-text text-muted">Width as percentage (1-100%)</small>
                        </div>

                        <div class="form-group">
                            <label for="facebook_comment_color_scheme">Color Scheme</label>
                            <select name="facebook_comment_color_scheme" class="form-control">
                                <option value="light" {{ get_setting('facebook_comment_color_scheme', 'light') == 'light' ? 'selected' : '' }}>Light</option>
                                <option value="dark" {{ get_setting('facebook_comment_color_scheme') == 'dark' ? 'selected' : '' }}>Dark</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Facebook Comments Settings</button>
                        </div>
                    </form>

                    <!-- Setup Instructions -->
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Setup Instructions</h5>
                            <ol>
                                <li>Create a Facebook App at <a href="https://developers.facebook.com/" target="_blank">developers.facebook.com</a></li>
                                <li>Add your domain to the App Domains in your Facebook App settings</li>
                                <li>Copy your App ID from the Facebook App dashboard</li>
                                <li>Paste the App ID in the field above</li>
                                <li>Configure comment settings and save</li>
                                <li>Comments will appear on job detail pages</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Current Status</h5>
                            <div class="alert {{ get_setting('facebook_comment_status') == 1 ? 'alert-success' : 'alert-warning' }}">
                                Facebook Comments are currently 
                                <strong>{{ get_setting('facebook_comment_status') == 1 ? 'Enabled' : 'Disabled' }}</strong>
                            </div>
                            
                            @if(get_setting('facebook_app_id'))
                                <div class="alert alert-info">
                                    <strong>App ID Configured:</strong><br>
                                    App ID: {{ get_setting('facebook_app_id') }}
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <strong>No App ID Configured</strong><br>
                                    Please add your Facebook App ID to enable comments.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Features -->
                    <hr>
                    <h5>Comment Features</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-comments fa-3x text-primary mb-3"></i>
                                    <h6>Social Engagement</h6>
                                    <p class="text-muted">Allow users to comment using their Facebook accounts</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-share fa-3x text-success mb-3"></i>
                                    <h6>Social Sharing</h6>
                                    <p class="text-muted">Comments can be shared on Facebook timelines</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-shield-alt fa-3x text-warning mb-3"></i>
                                    <h6>Spam Protection</h6>
                                    <p class="text-muted">Facebook's built-in spam and abuse protection</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview -->
                    @if(get_setting('facebook_comment_status') == 1 && get_setting('facebook_app_id'))
                    <hr>
                    <h5>Preview</h5>
                    <div class="border p-3 rounded">
                        <div id="fb-root"></div>
                        <script async defer crossorigin="anonymous" 
                            src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v18.0&appId={{ get_setting('facebook_app_id') }}">
                        </script>
                        
                        <div class="fb-comments" 
                            data-href="{{ url()->current() }}" 
                            data-width="{{ get_setting('facebook_comment_width', '100') }}%" 
                            data-numposts="{{ get_setting('facebook_comment_num_posts', '5') }}"
                            data-order-by="{{ get_setting('facebook_comment_order_by', 'social') }}"
                            data-colorscheme="{{ get_setting('facebook_comment_color_scheme', 'light') }}">
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
