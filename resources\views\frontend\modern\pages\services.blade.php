@extends('frontend.layouts.app')

@section('title', $page->title)
@section('meta_description', $metaDescription)
@section('meta_keywords', $metaKeywords)

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ $page->title }}</h1>
            <p class="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                Professional career services to accelerate your success
            </p>
        </div>
    </div>
</section>

<!-- Content Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($page->content)
            <div class="prose prose-lg max-w-none mb-12">
                {!! $page->content !!}
            </div>
        @endif

        @if($page->data && isset($page->data['sections']))
            @foreach($page->data['sections'] as $section)
                <div class="mb-16">
                    @if(isset($section['title']))
                        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">{{ $section['title'] }}</h2>
                    @endif
                    
                    @if(isset($section['items']))
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            @foreach($section['items'] as $item)
                                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-shadow duration-300">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $item['title'] }}</h3>
                                        <p class="text-gray-600 mb-4">{{ $item['description'] }}</p>
                                        @if(isset($item['link']))
                                            <a href="{{ $item['link'] }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                                                Learn More
                                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            @endforeach
        @endif

        <!-- Call to Action -->
        <div class="bg-gray-50 rounded-2xl p-8 text-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to Advance Your Career?</h3>
            <p class="text-gray-600 mb-6">Get started with our professional services today and take the next step in your career journey.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('jobs.search') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    Browse Jobs
                </a>
                <a href="{{ route('page.show', 'contact') }}" class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
