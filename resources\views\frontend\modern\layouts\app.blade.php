<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicon -->
    <link rel="icon" href="{{asset('images/favicon.png')}}" type="image/png">

    <!-- Meta Tags -->
    <meta name="author" content="JobOctopus">
    <meta name="robots" content="@yield('robots', 'index, follow')">

    @if(isset($seoData))
        {!! seo_meta_tags($seoData) !!}
    @else
        <title>@yield('title', 'Jobs-Recruitment-Employment-Career-Courses-Professionals | JobOctopus')</title>
        <meta name="description" content="@yield('meta_description', 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development')">
        <meta name="keywords" content="@yield('meta_keywords', 'jobs, recruitment, employment, career, courses, professionals, job search')">
        <link rel="canonical" href="{{ url()->current() }}">

        <!-- Open Graph -->
        <meta property="og:title" content="@yield('title', 'JobOctopus - Find Your Dream Job')">
        <meta property="og:description" content="@yield('meta_description', 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development')">
        <meta property="og:url" content="{{ url()->current() }}">
        <meta property="og:site_name" content="JobOctopus">
        <meta property="og:type" content="website">
        <meta property="og:image" content="{{ asset('images/logo.png') }}">

        <!-- Twitter Card -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="@yield('title', 'JobOctopus - Find Your Dream Job')">
        <meta name="twitter:description" content="@yield('meta_description', 'Find your dream job with JobOctopus')">
        <meta name="twitter:image" content="{{ asset('images/logo.png') }}">
    @endif

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Vite Assets (Tailwind CSS + Alpine.js) -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <style>
        @yield('styles')
    </style>
    @stack('styles')

    <!-- Structured Data -->
    @if(isset($structuredData))
        {!! structured_data($structuredData) !!}
    @endif

    @stack('structured-data')
</head>
<body class="font-inter antialiased bg-gray-50" x-data="{ mobileMenuOpen: false }"
      x-init="
        // Add smooth scrolling
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });
      ">

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200" x-data="{
        jobTypesOpen: false,
        servicesOpen: false,
        businessOpen: false,
        closeAllDropdowns() {
            this.jobTypesOpen = false;
            this.servicesOpen = false;
            this.businessOpen = false;
        }
    }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{url('/')}}" class="flex items-center">
                        <img src="{{asset('images/logo.png')}}" alt="JobOctopus" class="h-10 w-auto">
                        <span class="ml-2 text-xl font-bold text-gray-800 hidden sm:block">JobOctopus</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-1">
                    <!-- Home -->
                    <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fa fa-home mr-1"></i>Home
                    </a>

                    <!-- Job Types Dropdown -->
                    <div class="relative" @mouseenter="jobTypesOpen = true" @mouseleave="jobTypesOpen = false">
                        <button class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            <i class="fa fa-briefcase mr-1"></i>Job Types
                            <svg class="ml-1 h-4 w-4 transition-transform" :class="jobTypesOpen ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <div x-show="jobTypesOpen"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                            <div class="grid grid-cols-2 gap-1 p-2">
                                <a href="{{ route('jobs.search', ['job_type' => 'volunteering']) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-md transition-colors">
                                    <i class="fa fa-heart text-red-500 mr-2 w-4"></i>Volunteering
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'casual']) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-md transition-colors">
                                    <i class="fa fa-clock-o text-green-500 mr-2 w-4"></i>Casual
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'internship']) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-md transition-colors">
                                    <i class="fa fa-graduation-cap text-blue-500 mr-2 w-4"></i>Internship
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'part-time']) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-md transition-colors">
                                    <i class="fa fa-clock text-orange-500 mr-2 w-4"></i>Part-time
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'full-time']) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-md transition-colors">
                                    <i class="fa fa-briefcase text-purple-500 mr-2 w-4"></i>Full-time
                                </a>
                                <a href="{{ route('jobs.search', ['remote_option' => '1']) }}" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-md transition-colors">
                                    <i class="fa fa-laptop text-indigo-500 mr-2 w-4"></i>Online
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Services Dropdown -->
                    <div class="relative" @mouseenter="servicesOpen = true" @mouseleave="servicesOpen = false">
                        <button class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            <i class="fa fa-cogs mr-1"></i>Services
                            <svg class="ml-1 h-4 w-4 transition-transform" :class="servicesOpen ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <div x-show="servicesOpen"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             class="absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                            <a href="{{ route('page.show', 'career-guidance') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-compass text-blue-500 mr-3 w-4"></i>Career Guidance
                            </a>
                            <a href="{{ route('page.show', 'training') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-book text-green-500 mr-3 w-4"></i>Training
                            </a>
                            <a href="{{ route('page.show', 'resume-writing') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-file-text text-purple-500 mr-3 w-4"></i>Resume Writing
                            </a>
                            <a href="{{ route('page.show', 'job-seeker-login') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-sign-in text-indigo-500 mr-3 w-4"></i>Job Seeker Login
                            </a>
                        </div>
                    </div>

                    <!-- Business Solutions Dropdown -->
                    <div class="relative" @mouseenter="businessOpen = true" @mouseleave="businessOpen = false">
                        <button class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            <i class="fa fa-building mr-1"></i>Business
                            <svg class="ml-1 h-4 w-4 transition-transform" :class="businessOpen ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <div x-show="businessOpen"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             class="absolute left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                            <a href="{{ route('page.show', 'employers-portal') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-users text-blue-500 mr-3 w-4"></i>Employers Portal
                            </a>
                            <a href="{{ route('page.show', 'recruiters-login') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-user-tie text-green-500 mr-3 w-4"></i>Recruiters Login
                            </a>
                            <a href="{{ route('page.show', 'franchise') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-handshake text-purple-500 mr-3 w-4"></i>Franchise
                            </a>
                            <a href="{{ route('page.show', 'business-solutions') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                <i class="fa fa-lightbulb text-orange-500 mr-3 w-4"></i>Business Solutions
                            </a>
                        </div>
                    </div>

                    <!-- Companies -->
                    <a href="{{ route('frontend.companies') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fa fa-building-o mr-1"></i>Companies
                    </a>

                    <!-- About -->
                    <a href="{{ route('page.show', 'about') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fa fa-info-circle mr-1"></i>About
                    </a>

                    <!-- Contact -->
                    <a href="{{ route('page.show', 'contact') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fa fa-envelope mr-1"></i>Contact
                    </a>
                </div>

                <!-- Auth Links -->
                <div class="hidden lg:flex items-center space-x-3">
                    @auth
                        <a href="{{ route('dashboard') }}" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            <i class="fa fa-dashboard mr-1"></i>Dashboard
                        </a>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                                <i class="fa fa-user-circle mr-1"></i>{{ auth()->user()->name }}
                                <svg class="ml-1 h-4 w-4 transition-transform" :class="open ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                <a href="{{ route('profile.edit') }}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-colors">
                                    <i class="fa fa-user mr-2"></i>Profile
                                </a>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors">
                                        <i class="fa fa-sign-out mr-2"></i>Logout
                                    </button>
                                </form>
                            </div>
                        </div>
                    @else
                        <a href="{{ route('login') }}" class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors">
                            <i class="fa fa-sign-in mr-1"></i>Login
                        </a>
                        <a href="{{ route('register') }}" class="flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-md">
                            <i class="fa fa-user-plus mr-1"></i>Register
                        </a>
                    @endauth
                </div>

                <!-- Mobile menu button -->
                <div class="lg:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen; closeAllDropdowns()" class="text-gray-700 hover:text-blue-600 p-2">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div x-show="mobileMenuOpen"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 -translate-y-2"
             x-transition:enter-end="opacity-100 translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0"
             x-transition:leave-end="opacity-0 -translate-y-2"
             class="lg:hidden bg-white border-t border-gray-200 shadow-lg"
             x-data="{
                 mobileJobTypesOpen: false,
                 mobileServicesOpen: false,
                 mobileBusinessOpen: false
             }">
            <div class="px-4 py-4 space-y-2 max-h-96 overflow-y-auto">
                <!-- Home -->
                <a href="{{ route('home') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                    <i class="fa fa-home mr-3 w-4"></i>Home
                </a>

                <!-- Job Types Section -->
                <div class="border-b border-gray-100 pb-2">
                    <button @click="mobileJobTypesOpen = !mobileJobTypesOpen" class="flex items-center justify-between w-full px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                        <span class="flex items-center">
                            <i class="fa fa-briefcase mr-3 w-4"></i>Job Types
                        </span>
                        <svg class="h-4 w-4 transition-transform" :class="mobileJobTypesOpen ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div x-show="mobileJobTypesOpen" x-transition class="ml-6 mt-2 space-y-1">
                        <a href="{{ route('jobs.search', ['job_type' => 'volunteering']) }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-heart text-red-500 mr-2 w-4"></i>Volunteering
                        </a>
                        <a href="{{ route('jobs.search', ['job_type' => 'casual']) }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-clock-o text-green-500 mr-2 w-4"></i>Casual
                        </a>
                        <a href="{{ route('jobs.search', ['job_type' => 'internship']) }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-graduation-cap text-blue-500 mr-2 w-4"></i>Internship
                        </a>
                        <a href="{{ route('jobs.search', ['job_type' => 'part-time']) }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-clock text-orange-500 mr-2 w-4"></i>Part-time
                        </a>
                        <a href="{{ route('jobs.search', ['job_type' => 'full-time']) }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-briefcase text-purple-500 mr-2 w-4"></i>Full-time
                        </a>
                        <a href="{{ route('jobs.search', ['remote_option' => '1']) }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-laptop text-indigo-500 mr-2 w-4"></i>Online
                        </a>
                    </div>
                </div>

                <!-- Services Section -->
                <div class="border-b border-gray-100 pb-2">
                    <button @click="mobileServicesOpen = !mobileServicesOpen" class="flex items-center justify-between w-full px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                        <span class="flex items-center">
                            <i class="fa fa-cogs mr-3 w-4"></i>Services
                        </span>
                        <svg class="h-4 w-4 transition-transform" :class="mobileServicesOpen ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div x-show="mobileServicesOpen" x-transition class="ml-6 mt-2 space-y-1">
                        <a href="{{ route('page.show', 'career-guidance') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-compass text-blue-500 mr-2 w-4"></i>Career Guidance
                        </a>
                        <a href="{{ route('page.show', 'training') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-book text-green-500 mr-2 w-4"></i>Training
                        </a>
                        <a href="{{ route('page.show', 'resume-writing') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-file-text text-purple-500 mr-2 w-4"></i>Resume Writing
                        </a>
                        <a href="{{ route('page.show', 'job-seeker-login') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-sign-in text-indigo-500 mr-2 w-4"></i>Job Seeker Login
                        </a>
                    </div>
                </div>

                <!-- Business Section -->
                <div class="border-b border-gray-100 pb-2">
                    <button @click="mobileBusinessOpen = !mobileBusinessOpen" class="flex items-center justify-between w-full px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                        <span class="flex items-center">
                            <i class="fa fa-building mr-3 w-4"></i>Business
                        </span>
                        <svg class="h-4 w-4 transition-transform" :class="mobileBusinessOpen ? 'rotate-180' : ''" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    <div x-show="mobileBusinessOpen" x-transition class="ml-6 mt-2 space-y-1">
                        <a href="{{ route('page.show', 'employers-portal') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-users text-blue-500 mr-2 w-4"></i>Employers Portal
                        </a>
                        <a href="{{ route('page.show', 'recruiters-login') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-user-tie text-green-500 mr-2 w-4"></i>Recruiters Login
                        </a>
                        <a href="{{ route('page.show', 'franchise') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-handshake text-purple-500 mr-2 w-4"></i>Franchise
                        </a>
                        <a href="{{ route('page.show', 'business-solutions') }}" class="flex items-center px-3 py-2 text-sm text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-lightbulb text-orange-500 mr-2 w-4"></i>Business Solutions
                        </a>
                    </div>
                </div>

                <!-- Other Main Links -->
                <a href="{{ route('frontend.companies') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                    <i class="fa fa-building-o mr-3 w-4"></i>Companies
                </a>
                <a href="{{ route('page.show', 'about') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                    <i class="fa fa-info-circle mr-3 w-4"></i>About
                </a>
                <a href="{{ route('page.show', 'contact') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                    <i class="fa fa-envelope mr-3 w-4"></i>Contact
                </a>

                <!-- Auth Section -->
                <div class="border-t border-gray-200 pt-3 mt-3">
                    @auth
                        <a href="{{ route('dashboard') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-dashboard mr-3 w-4"></i>Dashboard
                        </a>
                        <a href="{{ route('profile.edit') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-user mr-3 w-4"></i>Profile
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="block">
                            @csrf
                            <button type="submit" class="flex items-center w-full px-3 py-2 text-gray-700 hover:bg-red-50 hover:text-red-600 rounded-lg transition-colors">
                                <i class="fa fa-sign-out mr-3 w-4"></i>Logout
                            </button>
                        </form>
                    @else
                        <a href="{{ route('login') }}" class="flex items-center px-3 py-2 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-lg transition-colors">
                            <i class="fa fa-sign-in mr-3 w-4"></i>Login
                        </a>
                        <a href="{{ route('register') }}" class="flex items-center px-3 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-300 mt-2">
                            <i class="fa fa-user-plus mr-3 w-4"></i>Register
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    @if(isset($breadcrumbs) && !empty($breadcrumbs))
        <div class="bg-gray-50 border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                {!! breadcrumb_html($breadcrumbs) !!}
            </div>
        </div>
    @endif

    <!-- Main Content -->
    @yield('content')

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-1">
                    <div class="flex items-center mb-4">
                        <img src="{{asset('images/logo.png')}}" alt="JobOctopus" class="h-10 w-auto filter brightness-0 invert">
                        <span class="ml-2 text-xl font-bold text-white">JobOctopus</span>
                    </div>
                    <p class="text-gray-400 text-sm">Find your dream job with JobOctopus - the leading job portal for professionals. Connect with top employers and advance your career.</p>
                    <div class="flex space-x-4 mt-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <img src="{{asset('images/facebook.png')}}" alt="Facebook" class="h-6 w-6">
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <img src="{{asset('images/twitter.png')}}" alt="Twitter" class="h-6 w-6">
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <img src="{{asset('images/instagram.png')}}" alt="Instagram" class="h-6 w-6">
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <img src="{{asset('images/youtube.png')}}" alt="YouTube" class="h-6 w-6">
                        </a>
                    </div>
                </div>
                <div>
                    <h3 class="text-white font-semibold mb-4">For Candidates</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="{{ route('jobs.all') }}" class="hover:text-white">Browse Jobs</a></li>
                        <li><a href="{{ route('register') }}" class="hover:text-white">Create Account</a></li>
                        <li><a href="{{ route('login') }}" class="hover:text-white">Login</a></li>
                        <li><a href="{{ route('page.show', 'faq') }}" class="hover:text-white">FAQ</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="{{ route('login') }}" class="hover:text-white">Employer Login</a></li>
                        <li><a href="{{ route('register') }}" class="hover:text-white">Post Jobs</a></li>
                        <li><a href="{{ route('page.show', 'membership') }}" class="hover:text-white">Pricing</a></li>
                        <li><a href="{{ route('page.show', 'advertise') }}" class="hover:text-white">Advertise</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="{{ route('page.show', 'contact') }}" class="hover:text-white">Contact Us</a></li>
                        <li><a href="{{ route('page.show', 'about') }}" class="hover:text-white">About Us</a></li>
                        <li><a href="{{ route('page.show', 'privacy-policy') }}" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="{{ route('page.show', 'terms-conditions') }}" class="hover:text-white">Terms & Conditions</a></li>
                    </ul>
                </div>
            </div>

            <!-- Social Links & Newsletter -->
            <div class="border-t border-gray-800 mt-8 pt-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-white font-semibold mb-4">Follow Us</h4>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-facebook text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-twitter text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-linkedin text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-instagram text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-youtube text-xl"></i>
                            </a>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-white font-semibold mb-4">Newsletter</h4>
                        <form class="flex">
                            <input
                                type="email"
                                placeholder="Enter your email"
                                class="flex-1 px-4 py-2 rounded-l-md border-0 text-gray-900 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                            >
                            <button
                                type="submit"
                                class="bg-gradient-to-br from-red-600 to-red-700 text-white px-6 py-2 rounded-r-md hover:shadow-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5"
                            >
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400 text-sm">
                    Copyright &copy; <span id="current-year"></span> JobOctopus. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Set current year
        document.getElementById('current-year').textContent = new Date().getFullYear();
    </script>
    @stack('scripts')
</body>
</html>
