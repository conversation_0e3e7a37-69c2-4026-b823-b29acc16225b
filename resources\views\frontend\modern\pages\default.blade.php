@extends('frontend.layouts.app')

@section('title', $metaTitle ?? $page->title)
@section('meta_description', $metaDescription ?? '')
@section('meta_keywords', $metaKeywords ?? '')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    {{ $page->title }}
                </h1>
                @if($page->meta_description)
                    <p class="text-xl text-white opacity-90 max-w-3xl mx-auto">
                        {{ $page->meta_description }}
                    </p>
                @endif
            </div>
        </div>
    </section>

    <!-- Content Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                @if($page->content)
                    <div class="prose prose-lg max-w-none">
                        {!! $page->content !!}
                    </div>
                @endif

                @if($page->data && isset($page->data['sections']))
                    @foreach($page->data['sections'] as $section)
                        <div class="mt-12">
                            @if(isset($section['title']))
                                <h2 class="text-2xl font-bold text-gray-900 mb-6">{{ $section['title'] }}</h2>
                            @endif
                            
                            @if(isset($section['content']))
                                <div class="prose prose-lg max-w-none">
                                    {!! $section['content'] !!}
                                </div>
                            @endif
                            
                            @if(isset($section['items']) && is_array($section['items']))
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                                    @foreach($section['items'] as $item)
                                        <div class="bg-gray-50 rounded-lg p-6">
                                            @if(isset($item['title']))
                                                <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ $item['title'] }}</h3>
                                            @endif
                                            @if(isset($item['description']))
                                                <p class="text-gray-600">{{ $item['description'] }}</p>
                                            @endif
                                            @if(isset($item['link']))
                                                <a href="{{ $item['link'] }}" class="inline-block mt-3 text-blue-600 hover:text-blue-700 font-medium">
                                                    Learn More →
                                                </a>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </section>
</div>
@endsection
