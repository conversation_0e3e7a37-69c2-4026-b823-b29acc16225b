@extends('frontend.layouts.app')

@section('title', $metaTitle ?? $page->title)
@section('meta_description', $metaDescription ?? '')
@section('meta_keywords', $metaKeywords ?? '')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    {{ $page->title }}
                </h1>
                @if($page->meta_description)
                    <p class="text-xl text-white opacity-90 max-w-3xl mx-auto">
                        {{ $page->meta_description }}
                    </p>
                @endif
            </div>
        </div>
    </section>

    <!-- Content Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($page->content)
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-12">
                    <div class="prose prose-lg max-w-none">
                        {!! $page->content !!}
                    </div>
                </div>
            @endif

            @if($page->data && isset($page->data['sections']))
                @foreach($page->data['sections'] as $section)
                    <div class="mb-12">
                        @if(isset($section['title']))
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ $section['title'] }}</h2>
                                <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-red-500 mx-auto rounded-full"></div>
                            </div>
                        @endif
                        
                        @if(isset($section['content']))
                            <div class="prose prose-lg max-w-none mb-8">
                                {!! $section['content'] !!}
                            </div>
                        @endif
                        
                        @if(isset($section['items']) && is_array($section['items']))
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                @foreach($section['items'] as $item)
                                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 group">
                                        <div class="flex items-center mb-4">
                                            <div class="w-12 h-12 red-gradient rounded-lg flex items-center justify-center mr-4">
                                                <i class="fa fa-graduation-cap text-white text-xl"></i>
                                            </div>
                                            @if(isset($item['title']))
                                                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ $item['title'] }}</h3>
                                            @endif
                                        </div>
                                        @if(isset($item['description']))
                                            <p class="text-gray-600 mb-4 leading-relaxed">{{ $item['description'] }}</p>
                                        @endif
                                        @if(isset($item['link']))
                                            <a href="{{ $item['link'] }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors">
                                                Learn More 
                                                <i class="fa fa-arrow-right ml-2 text-sm"></i>
                                            </a>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endforeach
            @endif

            <!-- Additional Training Features -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Why Choose Our Training Partners?</h2>
                    <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-red-500 mx-auto rounded-full"></div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="w-16 h-16 icon-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-certificate text-white text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Certified Courses</h3>
                        <p class="text-gray-600 text-sm">Industry-recognized certifications</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 red-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-users text-white text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Expert Instructors</h3>
                        <p class="text-gray-600 text-sm">Learn from industry professionals</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 icon-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-clock-o text-white text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Flexible Schedule</h3>
                        <p class="text-gray-600 text-sm">Learn at your own pace</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 red-gradient rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-briefcase text-white text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Job Placement</h3>
                        <p class="text-gray-600 text-sm">Career support and placement assistance</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
