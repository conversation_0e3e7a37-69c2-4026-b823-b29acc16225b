<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Page;

class PagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about',
                'content' => '<h1>About JobOctopus</h1>
                <p>JobOctopus is a leading job portal that connects talented professionals with top employers worldwide. Our mission is to make job searching and hiring more efficient, transparent, and successful for everyone involved.</p>
                
                <h2>Our Vision</h2>
                <p>To become the most trusted platform for career advancement and talent acquisition globally.</p>
                
                <h2>Our Mission</h2>
                <p>We strive to bridge the gap between job seekers and employers by providing innovative tools, comprehensive resources, and personalized experiences that lead to meaningful career connections.</p>
                
                <h2>Why Choose JobOctopus?</h2>
                <ul>
                    <li>Extensive job database across multiple industries</li>
                    <li>Advanced search and filtering capabilities</li>
                    <li>Professional career guidance and resources</li>
                    <li>Trusted by thousands of companies worldwide</li>
                    <li>User-friendly interface and mobile accessibility</li>
                </ul>',
                'meta_description' => 'Learn about JobOctopus - the leading job portal connecting professionals with top employers worldwide. Discover our mission, vision, and commitment to your career success.',
                'meta_keywords' => 'about joboctopus, job portal, career platform, employment, recruitment, about us',
                'template' => 'default',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'Contact Us',
                'slug' => 'contact',
                'content' => '<h1>Contact JobOctopus</h1>
                <p>We\'d love to hear from you! Get in touch with our team for any questions, suggestions, or support needs.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                    <div>
                        <h2>Get in Touch</h2>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <i class="fa fa-envelope text-blue-600 mr-3"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="flex items-center">
                                <i class="fa fa-phone text-blue-600 mr-3"></i>
                                <span>+****************</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fa fa-map-marker text-blue-600 mr-3"></i>
                                <span>123 Business Street, Suite 100<br>City, State 12345</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h2>Business Hours</h2>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Monday - Friday:</span>
                                <span>9:00 AM - 6:00 PM</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Saturday:</span>
                                <span>10:00 AM - 4:00 PM</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Sunday:</span>
                                <span>Closed</span>
                            </div>
                        </div>
                    </div>
                </div>',
                'meta_description' => 'Contact JobOctopus for support, questions, or business inquiries. Find our contact information, business hours, and get in touch with our team.',
                'meta_keywords' => 'contact joboctopus, support, help, customer service, contact information',
                'template' => 'contact',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => '<h1>Privacy Policy</h1>
                <p><strong>Last updated:</strong> ' . date('F d, Y') . '</p>
                
                <h2>Information We Collect</h2>
                <p>We collect information you provide directly to us, such as when you create an account, apply for jobs, or contact us for support.</p>
                
                <h2>How We Use Your Information</h2>
                <p>We use the information we collect to provide, maintain, and improve our services, process job applications, and communicate with you.</p>
                
                <h2>Information Sharing</h2>
                <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.</p>
                
                <h2>Data Security</h2>
                <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                
                <h2>Contact Us</h2>
                <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>',
                'meta_description' => 'Read JobOctopus Privacy Policy to understand how we collect, use, and protect your personal information on our job portal platform.',
                'meta_keywords' => 'privacy policy, data protection, personal information, joboctopus privacy',
                'template' => 'default',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => '<h1>Terms of Service</h1>
                <p><strong>Last updated:</strong> ' . date('F d, Y') . '</p>
                
                <h2>Acceptance of Terms</h2>
                <p>By accessing and using JobOctopus, you accept and agree to be bound by the terms and provision of this agreement.</p>
                
                <h2>Use License</h2>
                <p>Permission is granted to temporarily use JobOctopus for personal, non-commercial transitory viewing only.</p>
                
                <h2>User Accounts</h2>
                <p>When you create an account with us, you must provide information that is accurate, complete, and current at all times.</p>
                
                <h2>Prohibited Uses</h2>
                <p>You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts.</p>
                
                <h2>Termination</h2>
                <p>We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever.</p>
                
                <h2>Contact Information</h2>
                <p>If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.</p>',
                'meta_description' => 'Read JobOctopus Terms of Service to understand the rules and regulations for using our job portal platform.',
                'meta_keywords' => 'terms of service, terms and conditions, user agreement, joboctopus terms',
                'template' => 'default',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }
    }
}
