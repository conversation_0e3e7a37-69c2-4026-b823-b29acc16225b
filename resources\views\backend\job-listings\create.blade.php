@extends('backend.layouts.app')

@section('title', 'Create Job Listing')



@section('content')
<div class="max-w-6xl mx-auto py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Create Job Listing</h1>
                <p class="text-gray-600 mt-2">Post a new job opportunity and find the perfect candidate</p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-sm text-gray-500">
                    <i class="fa fa-clock-o mr-1"></i>
                    Auto-save enabled
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Steps -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="bg-gradient-to-br from-blue-500 to-purple-600 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold">1</div>
                <span class="text-sm font-medium text-gray-700">Basic Information</span>
            </div>
            <div class="flex-1 h-1 bg-gray-200 mx-4 rounded"></div>
            <div class="flex items-center space-x-4">
                <div class="bg-gray-300 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold">2</div>
                <span class="text-sm font-medium text-gray-500">Requirements</span>
            </div>
            <div class="flex-1 h-1 bg-gray-200 mx-4 rounded"></div>
            <div class="flex items-center space-x-4">
                <div class="bg-gray-300 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold">3</div>
                <span class="text-sm font-medium text-gray-500">Compensation</span>
            </div>
        </div>
    </div>

    <form action="{{ route('jobs-listing.store') }}" method="POST" x-data="jobForm()" class="space-y-8">
        @csrf

        <!-- Basic Information Section -->
        <div class="bg-gradient-to-br from-gray-50 to-white border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 rounded-xl p-8">
            <div class="flex items-center mb-6">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                    <i class="fa fa-info-circle text-white"></i>
                </div>
                <div>
                    <h2 class="text-xl font-semibold text-gray-900">Basic Information</h2>
                    <p class="text-gray-600 text-sm">Essential details about the job position</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="lg:col-span-2">
                    <label for="title" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fa fa-briefcase mr-2 text-blue-500"></i>Job Title *
                    </label>
                    <input type="text" name="title" id="title" value="{{ old('title') }}"
                           class="w-full rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-300 py-3 px-4 text-gray-900 placeholder-gray-500 @error('title') border-red-500 @enderror"
                           placeholder="e.g. Senior Software Engineer" required>
                    @error('title')
                        <p class="text-red-500 text-sm mt-2 flex items-center">
                            <i class="fa fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="lg:col-span-2">
                    <label for="description" class="block text-sm font-semibold text-gray-700 mb-2">
                        <i class="fa fa-file-text mr-2 text-green-500"></i>Job Description *
                    </label>
                    <textarea name="description" id="description" rows="6"
                              class="w-full rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-all duration-300 py-3 px-4 text-gray-900 placeholder-gray-500 @error('description') border-red-500 @enderror"
                              placeholder="Describe the role, company culture, and what makes this opportunity exciting..." required>{{ old('description') }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-2 flex items-center">
                            <i class="fa fa-exclamation-circle mr-1"></i>{{ $message }}
                        </p>
                    @enderror
                </div>
            </div>
        </div>

        <div class="mb-4">
            <label for="responsibilities" class="block font-medium text-gray-700">Responsibilities (one per line)</label>
            <textarea name="responsibilities[]" id="responsibilities" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('responsibilities') border-red-500 @enderror">{{ old('responsibilities') ? implode("\n", old('responsibilities')) : '' }}</textarea>
            @error('responsibilities')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="requirements" class="block font-medium text-gray-700">Requirements (one per line)</label>
            <textarea name="requirements[]" id="requirements" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('requirements') border-red-500 @enderror">{{ old('requirements') ? implode("\n", old('requirements')) : '' }}</textarea>
            @error('requirements')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="benefits" class="block font-medium text-gray-700">Benefits (one per line)</label>
            <textarea name="benefits[]" id="benefits" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('benefits') border-red-500 @enderror">{{ old('benefits') ? implode("\n", old('benefits')) : '' }}</textarea>
            @error('benefits')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="job_type" class="block font-medium text-gray-700">Job Type</label>
            <select name="job_type" id="job_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('job_type') border-red-500 @enderror" required>
                <option value="">Select job type</option>
                <option value="full-time" {{ old('job_type') == 'full-time' ? 'selected' : '' }}>Full Time</option>
                <option value="part-time" {{ old('job_type') == 'part-time' ? 'selected' : '' }}>Part Time</option>
                <option value="contract" {{ old('job_type') == 'contract' ? 'selected' : '' }}>Contract</option>
                <option value="freelance" {{ old('job_type') == 'freelance' ? 'selected' : '' }}>Freelance</option>
                <option value="internship" {{ old('job_type') == 'internship' ? 'selected' : '' }}>Internship</option>
            </select>
            @error('job_type')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="location" class="block font-medium text-gray-700">Location</label>
            <input type="text" name="location" id="location" value="{{ old('location') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('location') border-red-500 @enderror" required>
            @error('location')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4 flex items-center">
            <input type="checkbox" name="remote_option" id="remote_option" value="1" {{ old('remote_option') ? 'checked' : '' }} class="mr-2">
            <label for="remote_option" class="font-medium text-gray-700">Remote Option Available</label>
            @error('remote_option')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4 grid grid-cols-3 gap-4">
            <div>
                <label for="salary_min" class="block font-medium text-gray-700">Minimum Salary</label>
                <input type="number" name="salary_min" id="salary_min" value="{{ old('salary_min') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('salary_min') border-red-500 @enderror" step="0.01" min="0">
                @error('salary_min')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
            <div>
                <label for="salary_max" class="block font-medium text-gray-700">Maximum Salary</label>
                <input type="number" name="salary_max" id="salary_max" value="{{ old('salary_max') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('salary_max') border-red-500 @enderror" step="0.01" min="0">
                @error('salary_max')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
            <div>
                <label for="salary_currency" class="block font-medium text-gray-700">Currency</label>
                <input type="text" name="salary_currency" id="salary_currency" value="{{ old('salary_currency') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('salary_currency') border-red-500 @enderror" maxlength="10">
                @error('salary_currency')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div class="mb-4">
            <label for="salary_period" class="block font-medium text-gray-700">Salary Period</label>
            <select name="salary_period" id="salary_period" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('salary_period') border-red-500 @enderror">
                <option value="">Select salary period</option>
                <option value="hourly" {{ old('salary_period') == 'hourly' ? 'selected' : '' }}>Hourly</option>
                <option value="daily" {{ old('salary_period') == 'daily' ? 'selected' : '' }}>Daily</option>
                <option value="weekly" {{ old('salary_period') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                <option value="monthly" {{ old('salary_period') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                <option value="yearly" {{ old('salary_period') == 'yearly' ? 'selected' : '' }}>Yearly</option>
            </select>
            @error('salary_period')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="application_deadline" class="block font-medium text-gray-700">Application Deadline</label>
            <input type="date" name="application_deadline" id="application_deadline" value="{{ old('application_deadline') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('application_deadline') border-red-500 @enderror">
            @error('application_deadline')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="experience_level" class="block font-medium text-gray-700">Experience Level</label>
            <input type="text" name="experience_level" id="experience_level" value="{{ old('experience_level') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('experience_level') border-red-500 @enderror" maxlength="255">
            @error('experience_level')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="education_level" class="block font-medium text-gray-700">Education Level</label>
            <input type="text" name="education_level" id="education_level" value="{{ old('education_level') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('education_level') border-red-500 @enderror" maxlength="255">
            @error('education_level')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="skills_required" class="block font-medium text-gray-700">Skills Required (comma separated)</label>
            <input type="text" name="skills_required" id="skills_required" value="{{ old('skills_required') ? implode(',', old('skills_required')) : '' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('skills_required') border-red-500 @enderror">
            @error('skills_required')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="application_url" class="block font-medium text-gray-700">Application URL</label>
            <input type="url" name="application_url" id="application_url" value="{{ old('application_url') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('application_url') border-red-500 @enderror">
            @error('application_url')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="contact_email" class="block font-medium text-gray-700">Contact Email</label>
            <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('contact_email') border-red-500 @enderror">
            @error('contact_email')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-4">
            <label for="categories" class="block font-medium text-gray-700">Categories</label>
            <select name="categories[]" id="categories" multiple class="mt-1 block w-full rounded-md border-gray-300 shadow-sm @error('categories') border-red-500 @enderror" required>
                @foreach($categories as $category)
                    <option value="{{ $category->id }}" {{ (collect(old('categories'))->contains($category->id)) ? 'selected' : '' }}>{{ $category->name }}</option>
                @endforeach
            </select>
            @error('categories')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="flex justify-end">
            <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">Create Job Listing</button>
        </div>
    </form>
</div>
@endsection
