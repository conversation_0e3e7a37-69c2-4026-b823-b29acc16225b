@extends('frontend.layouts.app')

@section('title', $page->title)
@section('meta_description', $metaDescription)
@section('meta_keywords', $metaKeywords)

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-r from-green-600 to-blue-600 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">{{ $page->title }}</h1>
            <p class="text-xl md:text-2xl text-green-100 max-w-3xl mx-auto">
                Explore career opportunities across various industries
            </p>
        </div>
    </div>
</section>

<!-- Content Section -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        @if($page->content)
            <div class="prose prose-lg max-w-none mb-12 text-center">
                {!! $page->content !!}
            </div>
        @endif

        <!-- Job Categories Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @php
                $categories = \App\Models\Category::whereNull('parent_id')->withCount('jobListings')->get();
            @endphp
            
            @foreach($categories as $category)
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ $category->name }}</h3>
                        @if($category->description)
                            <p class="text-gray-600 mb-4">{{ Str::limit($category->description, 100) }}</p>
                        @endif
                        <div class="flex items-center justify-center text-sm text-gray-500 mb-4">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                            </svg>
                            {{ $category->job_listings_count }} {{ Str::plural('job', $category->job_listings_count) }}
                        </div>
                        <a href="{{ route('category.jobs', $category->slug) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            View Jobs
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Job Types Section -->
        <div class="mt-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">Browse by Job Type</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                @php
                    $jobTypes = [
                        ['name' => 'Full Time', 'slug' => 'full-time', 'icon' => 'clock'],
                        ['name' => 'Part Time', 'slug' => 'part-time', 'icon' => 'clock'],
                        ['name' => 'Contract', 'slug' => 'contract', 'icon' => 'document'],
                        ['name' => 'Freelance', 'slug' => 'freelance', 'icon' => 'user'],
                        ['name' => 'Internship', 'slug' => 'internship', 'icon' => 'academic-cap'],
                        ['name' => 'Volunteering', 'slug' => 'volunteering', 'icon' => 'heart']
                    ];
                @endphp
                
                @foreach($jobTypes as $type)
                    <a href="{{ route('jobs.search', ['job_type' => $type['slug']]) }}" class="bg-white rounded-lg shadow-md border border-gray-200 p-4 text-center hover:shadow-lg transition-shadow duration-200">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $type['name'] }}</span>
                    </a>
                @endforeach
            </div>
        </div>

        <!-- Call to Action -->
        <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center mt-16">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Can't Find What You're Looking For?</h3>
            <p class="text-gray-600 mb-6">Use our advanced search to find the perfect job opportunity for you.</p>
            <a href="{{ route('jobs.search') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                Advanced Job Search
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </a>
        </div>
    </div>
</section>
@endsection
