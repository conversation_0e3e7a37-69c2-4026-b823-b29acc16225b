<x-app-backend-layout>
    <div class="container mx-auto px-4 py-6">
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Social Login Configuration') }}</h4>

            <form action="{{ route('business_settings.social_login_update') }}" method="POST">
                @csrf
                <input type="hidden" name="type" value="social_login">

                <!-- Google Login Settings -->
                <div class="p-4 border border-gray-200 rounded-lg mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h5 class="text-lg font-medium text-gray-700">{{ __('Google Login') }}</h5>

                        <label class="toggle-switch">
                            <input type="checkbox"
                                   name="google_login"
                                   value="1"
                                   @if(get_setting('google_login') == 1) checked @endif>
                            <span class="toggle-track">
                                <span class="toggle-knob"></span>
                            </span>
                        </label>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="google_client_id" class="block mb-2 text-base font-medium text-gray-700">
                                {{ __('Client ID') }}
                            </label>
                            <input
                                type="text"
                                id="google_client_id"
                                name="GOOGLE_CLIENT_ID"
                                value="{{ env('GOOGLE_CLIENT_ID') }}"
                                placeholder="{{ __('Google Client ID') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>

                        <div>
                            <label for="google_client_secret" class="block mb-2 text-base font-medium text-gray-700">
                                {{ __('Client Secret') }}
                            </label>
                            <input
                                type="text"
                                id="google_client_secret"
                                name="GOOGLE_CLIENT_SECRET"
                                value="{{ env('GOOGLE_CLIENT_SECRET') }}"
                                placeholder="{{ __('Google Client Secret') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                    </div>

                    <div class="mt-4">
                        <label for="google_redirect_uri" class="block mb-2 text-base font-medium text-gray-700">
                            {{ __('Redirect URI') }}
                        </label>
                        <input
                            type="text"
                            id="google_redirect_uri"
                            name="GOOGLE_REDIRECT_URI"
                            value="{{ env('GOOGLE_REDIRECT_URI', url('/login/google/callback')) }}"
                            placeholder="{{ __('Google Redirect URI') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <p class="mt-2 text-sm text-gray-500">
                            {{ __('Use this URL in your Google OAuth credentials console') }}
                        </p>
                    </div>

                    <div class="mt-4">
                        <p class="text-sm text-gray-600">
                            {{ __('How to get Google Client ID and Client Secret:') }}
                        </p>
                        <ol class="list-decimal list-inside text-sm text-gray-600 mt-2 space-y-1">
                            <li>{{ __('Go to the Google Cloud Console (https://console.cloud.google.com/)') }}</li>
                            <li>{{ __('Create a new project or select an existing one') }}</li>
                            <li>{{ __('Navigate to "APIs & Services" > "Credentials"') }}</li>
                            <li>{{ __('Click "Create Credentials" > "OAuth client ID"') }}</li>
                            <li>{{ __('Set the application type to "Web application"') }}</li>
                            <li>{{ __('Add the redirect URI shown above to the authorized redirect URIs') }}</li>
                            <li>{{ __('Click "Create" and copy the Client ID and Client Secret') }}</li>
                        </ol>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-red-600 transition-colors">
                        {{ __('Save Changes') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</x-app-backend-layout>
