@extends('backend.layouts.app')

@section('title', 'Google reCAPTCHA Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Google reCAPTCHA Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('business_settings.update') }}" method="POST">
                        @csrf
                        <input type="hidden" name="types[]" value="recaptcha_status">
                        <input type="hidden" name="types[]" value="RECAPTCHA_SITE_KEY">
                        <input type="hidden" name="types[]" value="RECAPTCHA_SECRET_KEY">
                        <input type="hidden" name="types[]" value="recaptcha_version">

                        <div class="form-group">
                            <label class="form-label">Enable Google reCAPTCHA</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="recaptcha_status" 
                                    {{ get_setting('recaptcha_status') == 1 ? 'checked' : '' }}>
                                <label class="form-check-label">Enable reCAPTCHA protection</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>reCAPTCHA Version</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recaptcha_version" value="v2" 
                                    {{ get_setting('recaptcha_version', 'v2') == 'v2' ? 'checked' : '' }}>
                                <label class="form-check-label">reCAPTCHA v2 (I'm not a robot checkbox)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recaptcha_version" value="v3" 
                                    {{ get_setting('recaptcha_version') == 'v3' ? 'checked' : '' }}>
                                <label class="form-check-label">reCAPTCHA v3 (Invisible, score-based)</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="RECAPTCHA_SITE_KEY">reCAPTCHA Site Key</label>
                            <input type="text" name="RECAPTCHA_SITE_KEY" class="form-control" 
                                value="{{ env('RECAPTCHA_SITE_KEY') }}" 
                                placeholder="Enter your reCAPTCHA Site Key">
                            <small class="form-text text-muted">
                                This key is used in the HTML code your site serves to users
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="RECAPTCHA_SECRET_KEY">reCAPTCHA Secret Key</label>
                            <input type="password" name="RECAPTCHA_SECRET_KEY" class="form-control" 
                                value="{{ env('RECAPTCHA_SECRET_KEY') }}" 
                                placeholder="Enter your reCAPTCHA Secret Key">
                            <small class="form-text text-muted">
                                This key is used for communication between your site and reCAPTCHA
                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update reCAPTCHA Settings</button>
                        </div>
                    </form>

                    <!-- Setup Instructions -->
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Setup Instructions</h5>
                            <ol>
                                <li>Go to <a href="https://www.google.com/recaptcha/admin" target="_blank">Google reCAPTCHA Admin Console</a></li>
                                <li>Click "+" to create a new site</li>
                                <li>Enter your site label and domain</li>
                                <li>Choose reCAPTCHA type (v2 or v3)</li>
                                <li>Copy the Site Key and Secret Key</li>
                                <li>Paste the keys in the fields above</li>
                                <li>Enable reCAPTCHA and save settings</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Current Status</h5>
                            <div class="alert {{ get_setting('recaptcha_status') == 1 ? 'alert-success' : 'alert-warning' }}">
                                reCAPTCHA is currently 
                                <strong>{{ get_setting('recaptcha_status') == 1 ? 'Enabled' : 'Disabled' }}</strong>
                            </div>
                            
                            @if(get_setting('recaptcha_version'))
                                <div class="alert alert-info">
                                    <strong>Version:</strong> reCAPTCHA {{ strtoupper(get_setting('recaptcha_version', 'v2')) }}
                                </div>
                            @endif
                            
                            @if(env('RECAPTCHA_SITE_KEY') && env('RECAPTCHA_SECRET_KEY'))
                                <div class="alert alert-success">
                                    <strong>Configuration Complete!</strong><br>
                                    Site Key: {{ substr(env('RECAPTCHA_SITE_KEY'), 0, 20) }}...<br>
                                    Secret Key: ••••••••••••••••••••
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <strong>Configuration Incomplete</strong><br>
                                    Please provide both Site Key and Secret Key to enable reCAPTCHA.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Protection Areas -->
                    <hr>
                    <h5>Protected Areas</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-sign-in-alt fa-2x text-primary mb-2"></i>
                                    <h6>Login Forms</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-plus fa-2x text-success mb-2"></i>
                                    <h6>Registration</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                                    <h6>Contact Forms</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-key fa-2x text-danger mb-2"></i>
                                    <h6>Password Reset</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
