@extends('backend.layouts.app')

@section('title', 'Facebook Chat Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Facebook Chat Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('facebook_chat.update') }}" method="POST">
                        @csrf
                        
                        <div class="form-group">
                            <label class="form-label">Enable Facebook Chat</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="facebook_chat_status" 
                                    {{ get_setting('facebook_chat_status') == 1 ? 'checked' : '' }}>
                                <label class="form-check-label">Enable Facebook Messenger Chat Widget</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="facebook_page_id">Facebook Page ID</label>
                            <input type="text" name="facebook_page_id" class="form-control" 
                                value="{{ get_setting('facebook_page_id') }}" 
                                placeholder="Enter your Facebook Page ID">
                            <small class="form-text text-muted">
                                You can find your Page ID in your Facebook Page settings or by visiting 
                                <a href="https://findmyfbid.com/" target="_blank">findmyfbid.com</a>
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="facebook_app_id">Facebook App ID</label>
                            <input type="text" name="facebook_app_id" class="form-control" 
                                value="{{ get_setting('facebook_app_id') }}" 
                                placeholder="Enter your Facebook App ID">
                            <small class="form-text text-muted">
                                Create a Facebook App at <a href="https://developers.facebook.com/" target="_blank">developers.facebook.com</a>
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="facebook_chat_theme_color">Chat Theme Color</label>
                            <input type="color" name="facebook_chat_theme_color" class="form-control" 
                                value="{{ get_setting('facebook_chat_theme_color', '#0084ff') }}">
                        </div>

                        <div class="form-group">
                            <label for="facebook_chat_greeting">Greeting Message</label>
                            <textarea name="facebook_chat_greeting" class="form-control" rows="3" 
                                placeholder="Enter greeting message for visitors">{{ get_setting('facebook_chat_greeting', 'Hi! How can we help you?') }}</textarea>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Facebook Chat Settings</button>
                        </div>
                    </form>

                    <!-- Preview Section -->
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Setup Instructions</h5>
                            <ol>
                                <li>Create a Facebook App at <a href="https://developers.facebook.com/" target="_blank">developers.facebook.com</a></li>
                                <li>Add the "Messenger" product to your app</li>
                                <li>Get your Page ID from your Facebook Page settings</li>
                                <li>Copy your App ID from the Facebook App dashboard</li>
                                <li>Configure the settings above and save</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Current Status</h5>
                            <div class="alert {{ get_setting('facebook_chat_status') == 1 ? 'alert-success' : 'alert-warning' }}">
                                Facebook Chat is currently 
                                <strong>{{ get_setting('facebook_chat_status') == 1 ? 'Enabled' : 'Disabled' }}</strong>
                            </div>
                            
                            @if(get_setting('facebook_page_id') && get_setting('facebook_app_id'))
                                <div class="alert alert-info">
                                    <strong>Configuration Complete!</strong><br>
                                    Page ID: {{ get_setting('facebook_page_id') }}<br>
                                    App ID: {{ get_setting('facebook_app_id') }}
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <strong>Configuration Incomplete</strong><br>
                                    Please provide both Page ID and App ID to enable Facebook Chat.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
