@extends('backend.layouts.app')

@section('title', 'File System Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">File System Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('business_settings.update.activation') }}" method="POST">
                        @csrf
                        <input type="hidden" name="type" value="FILESYSTEM_DRIVER">

                        <div class="form-group">
                            <label>Select File System Driver</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="value" value="local" 
                                                    {{ env('FILESYSTEM_DRIVER') == 'local' ? 'checked' : '' }} id="local">
                                                <label class="form-check-label" for="local">
                                                    <i class="fas fa-hdd fa-3x text-primary mb-3"></i>
                                                    <h5>Local Storage</h5>
                                                    <p class="text-muted">Store files on local server</p>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="value" value="s3" 
                                                    {{ env('FILESYSTEM_DRIVER') == 's3' ? 'checked' : '' }} id="s3">
                                                <label class="form-check-label" for="s3">
                                                    <i class="fab fa-aws fa-3x text-warning mb-3"></i>
                                                    <h5>Amazon S3</h5>
                                                    <p class="text-muted">Store files on Amazon S3</p>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="value" value="public" 
                                                    {{ env('FILESYSTEM_DRIVER') == 'public' ? 'checked' : '' }} id="public">
                                                <label class="form-check-label" for="public">
                                                    <i class="fas fa-globe fa-3x text-success mb-3"></i>
                                                    <h5>Public Storage</h5>
                                                    <p class="text-muted">Store files in public directory</p>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update File System</button>
                        </div>
                    </form>

                    <!-- AWS S3 Configuration (shown when S3 is selected) -->
                    <div id="s3-config" style="display: {{ env('FILESYSTEM_DRIVER') == 's3' ? 'block' : 'none' }};">
                        <hr>
                        <h5>AWS S3 Configuration</h5>
                        <form action="{{ route('business_settings.update') }}" method="POST">
                            @csrf
                            <input type="hidden" name="types[]" value="AWS_ACCESS_KEY_ID">
                            <input type="hidden" name="types[]" value="AWS_SECRET_ACCESS_KEY">
                            <input type="hidden" name="types[]" value="AWS_DEFAULT_REGION">
                            <input type="hidden" name="types[]" value="AWS_BUCKET">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="AWS_ACCESS_KEY_ID">AWS Access Key ID</label>
                                        <input type="text" name="AWS_ACCESS_KEY_ID" class="form-control" 
                                            value="{{ env('AWS_ACCESS_KEY_ID') }}" placeholder="Your AWS Access Key">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="AWS_SECRET_ACCESS_KEY">AWS Secret Access Key</label>
                                        <input type="password" name="AWS_SECRET_ACCESS_KEY" class="form-control" 
                                            value="{{ env('AWS_SECRET_ACCESS_KEY') }}" placeholder="Your AWS Secret Key">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="AWS_DEFAULT_REGION">AWS Region</label>
                                        <input type="text" name="AWS_DEFAULT_REGION" class="form-control" 
                                            value="{{ env('AWS_DEFAULT_REGION') }}" placeholder="us-east-1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="AWS_BUCKET">AWS Bucket Name</label>
                                        <input type="text" name="AWS_BUCKET" class="form-control" 
                                            value="{{ env('AWS_BUCKET') }}" placeholder="your-bucket-name">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-success">Update AWS Settings</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const radios = document.querySelectorAll('input[name="value"]');
    const s3Config = document.getElementById('s3-config');
    
    radios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 's3') {
                s3Config.style.display = 'block';
            } else {
                s3Config.style.display = 'none';
            }
        });
    });
});
</script>
@endsection
