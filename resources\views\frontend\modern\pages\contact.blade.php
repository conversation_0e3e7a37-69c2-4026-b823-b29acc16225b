@extends('frontend.layouts.app')

@section('title', $metaTitle ?? $page->title)
@section('meta_description', $metaDescription ?? '')
@section('meta_keywords', $metaKeywords ?? '')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    {{ $page->title }}
                </h1>
                @if($page->meta_description)
                    <p class="text-xl text-white opacity-90 max-w-3xl mx-auto">
                        {{ $page->meta_description }}
                    </p>
                @endif
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Information -->
                <div>
                    @if($page->content)
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-8">
                            <div class="prose prose-lg max-w-none">
                                {!! $page->content !!}
                            </div>
                        </div>
                    @endif

                    @if($page->data && isset($page->data['contact_info']))
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                            <h2 class="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h2>
                            
                            <div class="space-y-6">
                                @if(isset($page->data['contact_info']['email']))
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 red-gradient rounded-lg flex items-center justify-center mr-4">
                                            <i class="fa fa-envelope text-white text-lg"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">Email</h3>
                                            <p class="text-gray-600">{{ $page->data['contact_info']['email'] }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if(isset($page->data['contact_info']['phone']))
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 icon-gradient rounded-lg flex items-center justify-center mr-4">
                                            <i class="fa fa-phone text-white text-lg"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">Phone</h3>
                                            <p class="text-gray-600">{{ $page->data['contact_info']['phone'] }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if(isset($page->data['contact_info']['address']))
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 red-gradient rounded-lg flex items-center justify-center mr-4">
                                            <i class="fa fa-map-marker text-white text-lg"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">Address</h3>
                                            <p class="text-gray-600">{{ $page->data['contact_info']['address'] }}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Contact Form -->
                <div>
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                        
                        <form class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                    <input type="text" id="first_name" name="first_name" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                    <input type="text" id="last_name" name="last_name" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                                </div>
                            </div>
                            
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" id="email" name="email" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                            </div>
                            
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" id="phone" name="phone" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            
                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                                <input type="text" id="subject" name="subject" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                            </div>
                            
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                                <textarea id="message" name="message" rows="6" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required></textarea>
                            </div>
                            
                            <div>
                                <button type="submit" class="w-full red-gradient text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
                                    <i class="fa fa-paper-plane mr-2"></i>
                                    Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
