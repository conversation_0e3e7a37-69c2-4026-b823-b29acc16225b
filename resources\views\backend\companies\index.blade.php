<x-app-backend-layout>
    <div class="max-w-7xl mx-auto px-6 py-8" x-data="companiesManager()">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Companies</h1>
                <p class="text-gray-600 mt-1">Manage companies on your platform</p>
            </div>
            <button @click="openCreateModal()"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Create Company
            </button>
        </div>

        @if(session('success'))
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    {{ session('success') }}
                </div>
            </div>
        @endif

        @if($companies->isEmpty())
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No companies</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating a new company.</p>
                <div class="mt-6">
                    <button @click="openCreateModal()"
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Create Company
                    </button>
                </div>
            </div>
        @else
            <div class="bg-white shadow-sm rounded-lg overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jobs</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($companies as $company)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-indigo-700">{{ substr($company->name, 0, 2) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $company->name }}</div>
                                        @if($company->website)
                                            <div class="text-sm text-gray-500">{{ $company->website }}</div>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $company->email }}</div>
                                @if($company->phone)
                                    <div class="text-sm text-gray-500">{{ $company->phone }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $company->job_listings_count ?? 0 }} jobs
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $company->created_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <button @click="viewCompany({{ $company->toJson() }})"
                                            class="text-indigo-600 hover:text-indigo-900 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                    <button @click="editCompany({{ $company->toJson() }})"
                                            class="text-yellow-600 hover:text-yellow-900 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                    </button>
                                    <button @click="deleteCompany({{ $company->id }}, '{{ $company->name }}')"
                                            class="text-red-600 hover:text-red-900 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            @if($companies->hasPages())
                <div class="mt-6">
                    {{ $companies->links() }}
                </div>
            @endif
        @endif

        <!-- Create/Edit Modal -->
        <div x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-50 overflow-y-auto"
             style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal()"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
                    <form @submit.prevent="submitForm()">
                        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                            <div class="sm:flex sm:items-start">
                                <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4" x-text="modalTitle"></h3>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label for="company_name" class="block text-sm font-medium text-gray-700">Company Name</label>
                                            <input type="text"
                                                   id="company_name"
                                                   x-model="form.name"
                                                   :readonly="modalMode === 'view'"
                                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                   :class="{'bg-gray-50': modalMode === 'view'}"
                                                   required>
                                            <div x-show="errors.name" class="mt-1 text-sm text-red-600" x-text="errors.name"></div>
                                        </div>

                                        <div>
                                            <label for="company_email" class="block text-sm font-medium text-gray-700">Email</label>
                                            <input type="email"
                                                   id="company_email"
                                                   x-model="form.email"
                                                   :readonly="modalMode === 'view'"
                                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                   :class="{'bg-gray-50': modalMode === 'view'}"
                                                   required>
                                            <div x-show="errors.email" class="mt-1 text-sm text-red-600" x-text="errors.email"></div>
                                        </div>

                                        <div>
                                            <label for="company_phone" class="block text-sm font-medium text-gray-700">Phone</label>
                                            <input type="text"
                                                   id="company_phone"
                                                   x-model="form.phone"
                                                   :readonly="modalMode === 'view'"
                                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                   :class="{'bg-gray-50': modalMode === 'view'}">
                                            <div x-show="errors.phone" class="mt-1 text-sm text-red-600" x-text="errors.phone"></div>
                                        </div>

                                        <div>
                                            <label for="company_website" class="block text-sm font-medium text-gray-700">Website</label>
                                            <input type="url"
                                                   id="company_website"
                                                   x-model="form.website"
                                                   :readonly="modalMode === 'view'"
                                                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                   :class="{'bg-gray-50': modalMode === 'view'}">
                                            <div x-show="errors.website" class="mt-1 text-sm text-red-600" x-text="errors.website"></div>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <label for="company_description" class="block text-sm font-medium text-gray-700">Description</label>
                                        <textarea id="company_description"
                                                  x-model="form.description"
                                                  :readonly="modalMode === 'view'"
                                                  rows="3"
                                                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                                  :class="{'bg-gray-50': modalMode === 'view'}"></textarea>
                                        <div x-show="errors.description" class="mt-1 text-sm text-red-600" x-text="errors.description"></div>
                                    </div>

                                    <div x-show="modalMode === 'view'" class="mt-4 grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Created</label>
                                            <p class="mt-1 text-sm text-gray-900" x-text="form.created_at"></p>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700">Jobs Posted</label>
                                            <p class="mt-1 text-sm text-gray-900" x-text="form.job_listings_count + ' jobs'"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                            <template x-if="modalMode !== 'view'">
                                <button type="submit"
                                        :disabled="loading"
                                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
                                    <span x-show="!loading" x-text="modalMode === 'create' ? 'Create Company' : 'Update Company'"></span>
                                    <span x-show="loading">Processing...</span>
                                </button>
                            </template>
                            <button type="button"
                                    @click="closeModal()"
                                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                                <span x-text="modalMode === 'view' ? 'Close' : 'Cancel'"></span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div x-show="showDeleteModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 z-50 overflow-y-auto"
             style="display: none;">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeDeleteModal()"></div>

                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

                <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Delete Company</h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500">
                                        Are you sure you want to delete "<span x-text="deleteItem.name"></span>"? This action cannot be undone.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="button"
                                @click="confirmDelete()"
                                :disabled="loading"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
                            <span x-show="!loading">Delete</span>
                            <span x-show="loading">Deleting...</span>
                        </button>
                        <button type="button"
                                @click="closeDeleteModal()"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function companiesManager() {
            return {
                showModal: false,
                showDeleteModal: false,
                modalMode: 'create',
                modalTitle: '',
                loading: false,
                errors: {},
                form: {
                    id: null,
                    name: '',
                    email: '',
                    phone: '',
                    website: '',
                    description: '',
                    created_at: '',
                    job_listings_count: 0
                },
                deleteItem: { id: null, name: '' },

                openCreateModal() {
                    this.modalMode = 'create';
                    this.modalTitle = 'Create New Company';
                    this.resetForm();
                    this.showModal = true;
                },

                viewCompany(company) {
                    this.modalMode = 'view';
                    this.modalTitle = 'Company Details';
                    this.form = {
                        id: company.id,
                        name: company.name,
                        email: company.email,
                        phone: company.phone || '',
                        website: company.website || '',
                        description: company.description || '',
                        created_at: new Date(company.created_at).toLocaleDateString(),
                        job_listings_count: company.job_listings_count || 0
                    };
                    this.showModal = true;
                },

                editCompany(company) {
                    this.modalMode = 'edit';
                    this.modalTitle = 'Edit Company';
                    this.form = {
                        id: company.id,
                        name: company.name,
                        email: company.email,
                        phone: company.phone || '',
                        website: company.website || '',
                        description: company.description || '',
                        created_at: company.created_at,
                        job_listings_count: company.job_listings_count || 0
                    };
                    this.showModal = true;
                },

                deleteCompany(id, name) {
                    this.deleteItem = { id, name };
                    this.showDeleteModal = true;
                },

                closeModal() {
                    this.showModal = false;
                    this.resetForm();
                    this.errors = {};
                },

                closeDeleteModal() {
                    this.showDeleteModal = false;
                    this.deleteItem = { id: null, name: '' };
                },

                resetForm() {
                    this.form = {
                        id: null,
                        name: '',
                        email: '',
                        phone: '',
                        website: '',
                        description: '',
                        created_at: '',
                        job_listings_count: 0
                    };
                },

                async submitForm() {
                    this.loading = true;
                    this.errors = {};

                    try {
                        const url = this.modalMode === 'create'
                            ? '{{ route("companies.store") }}'
                            : `{{ url("companies") }}/${this.form.id}`;

                        const formData = new FormData();
                        formData.append('name', this.form.name);
                        formData.append('email', this.form.email);
                        formData.append('phone', this.form.phone);
                        formData.append('website', this.form.website);
                        formData.append('description', this.form.description);
                        formData.append('_token', '{{ csrf_token() }}');

                        if (this.modalMode === 'edit') {
                            formData.append('_method', 'PUT');
                        }

                        const response = await fetch(url, {
                            method: 'POST',
                            body: formData
                        });

                        if (response.ok) {
                            window.location.reload();
                        } else {
                            const data = await response.json();
                            if (data.errors) {
                                this.errors = data.errors;
                            } else {
                                alert('An error occurred. Please try again.');
                            }
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    } finally {
                        this.loading = false;
                    }
                },

                async confirmDelete() {
                    this.loading = true;

                    try {
                        const formData = new FormData();
                        formData.append('_token', '{{ csrf_token() }}');
                        formData.append('_method', 'DELETE');

                        const response = await fetch(`{{ url("companies") }}/${this.deleteItem.id}`, {
                            method: 'POST',
                            body: formData
                        });

                        if (response.ok) {
                            window.location.reload();
                        } else {
                            alert('An error occurred while deleting the company.');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }
    </script>
</x-app-backend-layout>