<?php

use App\Models\BusinessSetting;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

if (! function_exists('generate_unique_slug')) {
    /**
     * Generate a unique slug from a string.
     *
     * @param  string  $name
     * @param  string|null  $modelClass
     * @param  string  $column
     * @return string
     */
    function generate_unique_slug(string $name, string $modelClass = null, string $column = 'slug'): string
    {
        $slug = Str::slug($name);

        if ($modelClass) {
            $counter = 1;
            // Check if the slug already exists, and append a number to make it unique
            while ($modelClass::where($column, $slug)->exists()) {
                $slug = Str::slug($name) . '-' . $counter++;
            }
        }

        return $slug;
    }
}

if (!function_exists('get_setting')) {
    function get_setting($key, $default = null)
    {
        return BusinessSetting::where('type', $key)->value('value') ?? $default;
    }
}

if (!function_exists('translate')) {
    /**
     * Translate the given message.
     *
     * @param  string|null  $key
     * @param  array  $replace
     * @param  string|null  $locale
     * @return string|array|null
     */
    function translate($key = null, $replace = [], $locale = null)
    {
        return __($key, $replace, $locale);
    }
}

if (!function_exists('get_theme')) {
    /**
     * Get the currently active theme
     *
     * @return string
     */
    function get_theme()
    {
        static $theme = null;

        if ($theme === null) {
            // First check database setting, then fallback to environment variable, then default
            $theme = get_setting('active_theme') ?: env('APP_FRONTEND_THEME', 'modern');
        }

        return $theme;
    }
}

if (!function_exists('vite_theme_assets')) {
    /**
     * Generate Vite tags for theme assets
     *
     * @param string|array $theme The active theme name or array of assets
     * @return \Illuminate\Support\HtmlString
     */
    function vite_theme_assets($theme)
    {
        if (is_string($theme)) {
            // If a theme name is provided, build the asset paths
            $assets = [
                "resources/css/frontend/{$theme}/app.css",
                "resources/js/frontend/{$theme}/app.js"
            ];
        } else {
            // If array of assets is provided, use it directly
            $assets = $theme;
        }

        // Path to Vite manifest
        $manifestPath = public_path('build/.vite/manifest.json');

        // Check if manifest exists
        if (!file_exists($manifestPath)) {
            // Return empty string or fallback
            return new HtmlString('<!-- Vite manifest not found -->');
        }

        // Load manifest
        $manifest = json_decode(file_get_contents($manifestPath), true);

        $tags = [];

        foreach ($assets as $asset) {
            if (!isset($manifest[$asset])) {
                continue;
            }

            $url = asset('build/' . $manifest[$asset]['file']);

            // For CSS files
            if (str_ends_with($asset, '.css')) {
                $tags[] = "<link rel=\"stylesheet\" href=\"{$url}\">";
            }

            // For JS files
            if (str_ends_with($asset, '.js')) {
                $tags[] = "<script type=\"module\" src=\"{$url}\"></script>";
            }
        }

        return new HtmlString(implode("\n", $tags));
    }
}

if (!function_exists('frontend_trans')) {
    /**
     * Get a frontend translation string
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    function frontend_trans($key, $replace = [], $locale = null)
    {
        return trans('frontend.' . $key, $replace, $locale);
    }
}

if (!function_exists('backend_trans')) {
    /**
     * Get a backend translation string
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    function backend_trans($key, $replace = [], $locale = null)
    {
        return trans('backend.' . $key, $replace, $locale);
    }
}

/**
 * Set business setting value
 */
if (!function_exists('set_setting')) {
    function set_setting($type, $value, $lang = null)
    {
        return BusinessSetting::set($type, $value, $lang);
    }
}

/**
 * Format currency
 */
if (!function_exists('format_currency')) {
    function format_currency($amount, $currency = 'USD')
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
        ];

        $symbol = $symbols[$currency] ?? $currency;
        return $symbol . number_format($amount, 2);
    }
}

/**
 * Get user avatar
 */
if (!function_exists('get_avatar')) {
    function get_avatar($user, $size = 40)
    {
        if ($user && $user->avatar) {
            return asset('storage/' . $user->avatar);
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($user->name ?? 'User') . '&size=' . $size . '&background=random';
    }
}

/**
 * Truncate text
 */
if (!function_exists('truncate_text')) {
    function truncate_text($text, $length = 100, $suffix = '...')
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . $suffix;
    }
}

/**
 * Get time ago
 */
if (!function_exists('time_ago')) {
    function time_ago($datetime)
    {
        return \Carbon\Carbon::parse($datetime)->diffForHumans();
    }
}

/**
 * Check if user can access admin panel
 */
if (!function_exists('can_access_admin')) {
    function can_access_admin($user = null)
    {
        $user = $user ?? auth()->user();
        return $user && ($user->hasRole('superadmin') || $user->hasRole('admin'));
    }
}

/**
 * Get job type badge class
 */
if (!function_exists('get_job_type_badge')) {
    function get_job_type_badge($type)
    {
        $badges = [
            'full-time' => 'bg-green-100 text-green-800',
            'part-time' => 'bg-blue-100 text-blue-800',
            'contract' => 'bg-yellow-100 text-yellow-800',
            'freelance' => 'bg-purple-100 text-purple-800',
            'internship' => 'bg-pink-100 text-pink-800',
        ];

        return $badges[$type] ?? 'bg-gray-100 text-gray-800';
    }
}

/**
 * Get application status badge class
 */
if (!function_exists('get_status_badge')) {
    function get_status_badge($status)
    {
        $badges = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'reviewing' => 'bg-blue-100 text-blue-800',
            'shortlisted' => 'bg-green-100 text-green-800',
            'rejected' => 'bg-red-100 text-red-800',
            'hired' => 'bg-purple-100 text-purple-800',
        ];

        return $badges[$status] ?? 'bg-gray-100 text-gray-800';
    }
}