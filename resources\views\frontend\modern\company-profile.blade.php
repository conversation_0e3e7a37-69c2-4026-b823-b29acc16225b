@extends('frontend.layouts.app')

@section('title', $company->name . ' - Company Profile - JobOctopus')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Company Header -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col md:flex-row items-start md:items-center space-y-4 md:space-y-0 md:space-x-6">
                <!-- Company Logo -->
                <div class="flex-shrink-0">
                    @if($company->logo)
                        <img src="{{url('storage/' . $company->logo)}}" 
                             alt="{{$company->name}}" 
                             class="w-24 h-24 object-contain rounded-lg border border-gray-200 bg-white">
                    @else
                        <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fa fa-building text-white text-2xl"></i>
                        </div>
                    @endif
                </div>
                
                <!-- Company Info -->
                <div class="flex-1">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">{{$company->name}}</h1>
                    @if($company->industry)
                        <p class="text-lg text-gray-600 mb-2">{{$company->industry}}</p>
                    @endif
                    
                    <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                        @if($company->location)
                            <div class="flex items-center">
                                <i class="fa fa-map-marker-alt mr-2 text-gray-400"></i>
                                <span>{{$company->location}}</span>
                            </div>
                        @endif
                        
                        @if($company->size)
                            <div class="flex items-center">
                                <i class="fa fa-users mr-2 text-gray-400"></i>
                                <span>{{$company->size}} employees</span>
                            </div>
                        @endif
                        
                        @if($company->founded_year)
                            <div class="flex items-center">
                                <i class="fa fa-calendar mr-2 text-gray-400"></i>
                                <span>Founded {{$company->founded_year}}</span>
                            </div>
                        @endif
                        
                        @if($company->website)
                            <div class="flex items-center">
                                <i class="fa fa-globe mr-2 text-gray-400"></i>
                                <a href="{{$company->website}}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                    Visit Website
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-3">
                    @if($totalJobs > 0)
                        <a href="{{route('frontend.company.jobs', $company->slug)}}" 
                           class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                            View All Jobs ({{$totalJobs}})
                        </a>
                    @endif
                    <button class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        Follow Company
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Company Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- About Company -->
                @if($company->description)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">About {{$company->name}}</h2>
                        <div class="prose prose-gray max-w-none">
                            {!! nl2br(e($company->description)) !!}
                        </div>
                    </div>
                @endif

                <!-- Recent Jobs -->
                @if($recentJobs->count() > 0)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-semibold text-gray-900">Recent Job Openings</h2>
                            @if($totalJobs > $recentJobs->count())
                                <a href="{{route('frontend.company.jobs', $company->slug)}}" 
                                   class="text-blue-600 hover:text-blue-800 font-medium">
                                    View All ({{$totalJobs}})
                                </a>
                            @endif
                        </div>
                        
                        <div class="space-y-4">
                            @foreach($recentJobs as $job)
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{$job->title}}</h3>
                                            
                                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-3">
                                                @if($job->location)
                                                    <div class="flex items-center">
                                                        <i class="fa fa-map-marker-alt mr-1"></i>
                                                        <span>{{$job->location}}</span>
                                                    </div>
                                                @endif
                                                
                                                @if($job->job_type)
                                                    <div class="flex items-center">
                                                        <i class="fa fa-briefcase mr-1"></i>
                                                        <span>{{ucfirst(str_replace('_', ' ', $job->job_type))}}</span>
                                                    </div>
                                                @endif
                                                
                                                @if($job->salary_min && $job->salary_max)
                                                    <div class="flex items-center">
                                                        <i class="fa fa-dollar-sign mr-1"></i>
                                                        <span>${{number_format($job->salary_min)}} - ${{number_format($job->salary_max)}}</span>
                                                    </div>
                                                @endif
                                            </div>
                                            
                                            @if($job->categories->count() > 0)
                                                <div class="flex flex-wrap gap-2 mb-3">
                                                    @foreach($job->categories->take(3) as $category)
                                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                                            {{$category->name}}
                                                        </span>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                        
                                        <div class="flex flex-col items-end space-y-2">
                                            <span class="text-sm text-gray-500">{{$job->created_at->diffForHumans()}}</span>
                                            <a href="#" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                                Apply Now
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Company Stats -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Company Overview</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Open Positions</span>
                            <span class="font-semibold text-gray-900">{{$totalJobs}}</span>
                        </div>
                        
                        @if($company->size)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Company Size</span>
                                <span class="font-semibold text-gray-900">{{$company->size}}</span>
                            </div>
                        @endif
                        
                        @if($company->industry)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Industry</span>
                                <span class="font-semibold text-gray-900">{{$company->industry}}</span>
                            </div>
                        @endif
                        
                        @if($company->founded_year)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Founded</span>
                                <span class="font-semibold text-gray-900">{{$company->founded_year}}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Contact Information -->
                @if($company->email || $company->phone || $company->address)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        <div class="space-y-3">
                            @if($company->email)
                                <div class="flex items-center">
                                    <i class="fa fa-envelope w-5 text-gray-400 mr-3"></i>
                                    <a href="mailto:{{$company->email}}" class="text-blue-600 hover:text-blue-800">
                                        {{$company->email}}
                                    </a>
                                </div>
                            @endif
                            
                            @if($company->phone)
                                <div class="flex items-center">
                                    <i class="fa fa-phone w-5 text-gray-400 mr-3"></i>
                                    <a href="tel:{{$company->phone}}" class="text-blue-600 hover:text-blue-800">
                                        {{$company->phone}}
                                    </a>
                                </div>
                            @endif
                            
                            @if($company->address)
                                <div class="flex items-start">
                                    <i class="fa fa-map-marker-alt w-5 text-gray-400 mr-3 mt-1"></i>
                                    <span class="text-gray-600">{{$company->address}}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif

                <!-- Social Media -->
                @if($company->social_media && is_array($company->social_media) && count($company->social_media) > 0)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Follow Us</h3>
                        <div class="flex space-x-3">
                            @foreach($company->social_media as $platform => $url)
                                @if($url)
                                    <a href="{{$url}}" target="_blank" 
                                       class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
                                        <i class="fab fa-{{$platform}} text-gray-600"></i>
                                    </a>
                                @endif
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
