<?php

namespace App\Http\Controllers;

use App\Models\JobListing;
use App\Models\Category;
use App\Models\Company;
use App\Models\Page;
use App\Services\SeoService;
use App\Helpers\SecurityHelper;
use Illuminate\Http\Request;

/**
 * WebController - Cleaned up and dynamic version
 * Handles public frontend pages and job-related functionality
 */
class WebController extends Controller
{
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Homepage - Display featured content
     */
    public function index()
    {
        // Get featured job categories (limit to 6 for the homepage)
        $featuredCategories = Category::whereNull('parent_id')
            ->withCount('jobListings')
            ->take(6)
            ->get();

        // Get recent job listings (limit to 10 for the homepage)
        $recentJobs = JobListing::with(['company', 'categories'])
            ->where('status', 'published')
            ->latest()
            ->take(10)
            ->get();

        // Get top companies/recruiters (companies with most job listings)
        $topRecruiters = Company::withCount('jobListings')
            ->orderBy('job_listings_count', 'desc')
            ->take(10)
            ->get();

        // SEO Data
        $seoData = $this->seoService->generateMetaTags(
            'Find Your Dream Job | JobOctopus',
            'Discover thousands of job opportunities across various industries. Connect with top employers and advance your career with JobOctopus.',
            'jobs, careers, employment, job search, recruitment, hiring, work opportunities'
        );

        // Structured Data
        $structuredData = $this->seoService->generateWebsiteStructuredData();

        $theme = get_theme();
        return view("frontend.{$theme}.index", compact('featuredCategories', 'recentJobs', 'topRecruiters', 'seoData', 'structuredData'));
    }

    /**
     * Display jobs by category
     */
    public function categoryJobs($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();

        $jobs = JobListing::with(['company', 'categories'])
            ->whereHas('categories', function ($query) use ($category) {
                $query->where('categories.id', $category->id);
            })
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        // SEO Data
        $seoData = $this->seoService->generateCategorySeo($category);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Jobs', 'url' => url('/jobs')],
            ['name' => $category->name . ' Jobs', 'url' => '']
        ];

        // Structured Data
        $structuredData = $this->seoService->generateBreadcrumbStructuredData($breadcrumbs);

        $theme = get_theme();
        return view("frontend.{$theme}.category-jobs", compact('category', 'jobs', 'seoData', 'breadcrumbs', 'structuredData'));
    }

    /**
     * Display jobs by type (volunteering, casual, etc.)
     */
    public function jobsByType(Request $request)
    {
        $type = $request->route('type');

        $jobs = JobListing::with(['company', 'categories'])
            ->where('job_type', $type)
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        $title = ucfirst(str_replace('_', ' ', $type)) . ' Jobs';

        $theme = get_theme();
        return view("frontend.{$theme}.jobs-by-type", compact('jobs', 'type', 'title'));
    }

    /**
     * Search jobs with comprehensive filtering
     */
    public function searchJobs(Request $request)
    {
        // Validate and sanitize input
        $validated = $request->validate([
            'keywords' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'category' => 'nullable|integer|exists:categories,id',
            'job_type' => 'nullable|string|in:full_time,part_time,contract,freelance,internship,volunteering,casual,business,franchise',
            'salary_min' => 'nullable|integer|min:0',
            'salary_max' => 'nullable|integer|min:0',
            'experience_level' => 'nullable|string|in:entry,mid,senior,executive',
            'remote' => 'nullable|boolean'
        ]);

        $query = JobListing::with(['company', 'categories'])
            ->where('status', 'published');

        // Keywords search with proper sanitization
        if (!empty($validated['keywords'])) {
            $keywords = SecurityHelper::sanitizeSearchQuery($validated['keywords']);

            // Check for suspicious patterns
            if (SecurityHelper::containsSuspiciousPatterns($keywords)) {
                SecurityHelper::logSecurityEvent('Suspicious search query detected', ['query' => $keywords]);
                abort(400, 'Invalid search query');
            }

            $query->where(function ($q) use ($keywords) {
                $q->where('title', 'like', '%' . $keywords . '%')
                  ->orWhere('description', 'like', '%' . $keywords . '%')
                  ->orWhereHas('company', function ($companyQuery) use ($keywords) {
                      $companyQuery->where('name', 'like', '%' . $keywords . '%');
                  });
            });
        }

        // Location filter with sanitization
        if (!empty($validated['location'])) {
            $location = SecurityHelper::sanitizeSearchQuery($validated['location']);
            $query->where('location', 'like', '%' . $location . '%');
        }

        // Job type filter
        if (!empty($validated['job_type'])) {
            $query->where('job_type', $validated['job_type']);
        }

        // Category filter
        if (!empty($validated['category'])) {
            $query->whereHas('categories', function ($q) use ($validated) {
                $q->where('categories.id', $validated['category']);
            });
        }

        // Experience level filter
        if (!empty($validated['experience_level'])) {
            $query->where('experience_level', $validated['experience_level']);
        }

        // Salary range filter
        if (!empty($validated['salary_min'])) {
            $query->where('salary_max', '>=', $validated['salary_min']);
        }
        if (!empty($validated['salary_max'])) {
            $query->where('salary_min', '<=', $validated['salary_max']);
        }

        // Remote work filter
        if (isset($validated['remote'])) {
            $query->where('remote_option', $validated['remote']);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'latest');
        switch ($sortBy) {
            case 'salary_high':
                $query->orderBy('salary_max', 'desc');
                break;
            case 'salary_low':
                $query->orderBy('salary_min', 'asc');
                break;
            case 'oldest':
                $query->oldest();
                break;
            default:
                $query->latest();
                break;
        }

        $jobs = $query->paginate(20);

        // Get filter data for dropdowns
        $categories = Category::whereNull('parent_id')->with('children')->get();
        $jobTypes = ['full-time', 'part-time', 'contract', 'freelance', 'internship'];
        $experienceLevels = ['Entry Level', 'Mid Level', 'Senior Level', 'Director', 'Executive'];

        $searchParams = $request->only([
            'keywords', 'location', 'job_type', 'category', 'company', 'experience_level',
            'salary_min', 'salary_max', 'remote_option', 'date_posted', 'sort_by',
            'employment_types', 'work_location', 'date_from', 'date_to'
        ]);

        $theme = get_theme();
        return view("frontend.{$theme}.search-results", compact(
            'jobs', 'searchParams', 'categories', 'jobTypes', 'experienceLevels'
        ));
    }

    /**
     * Display all jobs
     */
    public function allJobs()
    {
        $jobs = JobListing::with(['company', 'categories'])
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        $theme = get_theme();
        return view("frontend.{$theme}.all-jobs", compact('jobs'));
    }

    /**
     * Display all companies (public frontend page)
     */
    public function companies()
    {
        $companies = Company::withCount('jobListings')
            ->orderBy('job_listings_count', 'desc')
            ->paginate(20);

        $theme = get_theme();
        return view("frontend.{$theme}.companies", compact('companies'));
    }

    /**
     * Display company profile page
     */
    public function companyProfile($slug)
    {
        $company = Company::where('slug', $slug)->firstOrFail();

        // Get recent jobs from this company
        $recentJobs = JobListing::with(['categories'])
            ->where('company_id', $company->id)
            ->where('status', 'published')
            ->latest()
            ->take(5)
            ->get();

        // Get total job count
        $totalJobs = JobListing::where('company_id', $company->id)
            ->where('status', 'published')
            ->count();

        // SEO Data
        $seoData = $this->seoService->generateCompanySeo($company);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Companies', 'url' => url('/companies')],
            ['name' => $company->name, 'url' => '']
        ];

        // Structured Data
        $structuredData = [
            $this->seoService->generateOrganizationStructuredData($company),
            $this->seoService->generateBreadcrumbStructuredData($breadcrumbs)
        ];

        $theme = get_theme();
        return view("frontend.{$theme}.company-profile", compact('company', 'recentJobs', 'totalJobs', 'seoData', 'breadcrumbs', 'structuredData'));
    }

    /**
     * Display jobs for a specific company
     */
    public function companyJobs($slug)
    {
        $company = Company::where('slug', $slug)->firstOrFail();

        $jobs = JobListing::with(['categories'])
            ->where('company_id', $company->id)
            ->where('status', 'published')
            ->latest()
            ->paginate(20);

        $theme = get_theme();
        return view("frontend.{$theme}.company-jobs", compact('company', 'jobs'));
    }

    /**
     * Display individual job listing (public page)
     */
    public function jobDetail($slug)
    {
        $job = JobListing::where('slug', $slug)
            ->where('status', 'published')
            ->with(['company', 'categories'])
            ->firstOrFail();

        // Increment view count
        $job->increment('views_count');

        // Check if the user has already applied
        $hasApplied = false;
        if (auth()->check() && auth()->user()->isCandidate()) {
            $hasApplied = auth()->user()->applications()
                ->where('job_listing_id', $job->id)
                ->exists();
        }

        // Get similar jobs
        $similarJobs = JobListing::where('id', '!=', $job->id)
            ->where('status', 'published')
            ->whereHas('categories', function ($query) use ($job) {
                $query->whereIn('categories.id', $job->categories->pluck('id'));
            })
            ->with(['company', 'categories'])
            ->take(3)
            ->get();

        // SEO Data
        $seoData = $this->seoService->generateJobSeo($job);

        // Breadcrumbs
        $breadcrumbs = [
            ['name' => 'Home', 'url' => url('/')],
            ['name' => 'Jobs', 'url' => url('/jobs')],
            ['name' => $job->title, 'url' => '']
        ];

        // Structured Data
        $structuredData = [
            $this->seoService->generateJobStructuredData($job),
            $this->seoService->generateBreadcrumbStructuredData($breadcrumbs)
        ];

        $theme = get_theme();
        return view("frontend.{$theme}.job-detail", compact('job', 'hasApplied', 'similarJobs', 'seoData', 'breadcrumbs', 'structuredData'));
    }

    /**
     * Handle dynamic category pages (accounting, development, technology, etc.)
     */
    public function dynamicCategoryPage($slug)
    {
        // Map of static page slugs to category names or job types
        $categoryMap = [
            'accounting' => 'Accounting',
            'development' => 'Development',
            'technology' => 'Technology',
            'medical' => 'Medical',
            'govt' => 'Government',
            'media_news' => 'Media & News',
            'business_management_jobs' => 'Business Management',
            'it_jobs' => 'Information Technology',
            'hospitality_jobs' => 'Hospitality',
            'manufacturing_jobs' => 'Manufacturing',
            'transporation_logistics_distribution_jobs' => 'Transportation & Logistics',
            'banking_insurance_jobs' => 'Banking & Insurance',
            'health_care_jobs' => 'Healthcare',
        ];

        // Check if this is a known category page
        if (isset($categoryMap[$slug])) {
            $categoryName = $categoryMap[$slug];

            // Try to find matching category in database
            $category = Category::where('name', 'LIKE', "%{$categoryName}%")->first();

            if ($category) {
                // Redirect to dynamic category page
                return redirect()->route('category.jobs', $category->slug);
            } else {
                // Create a virtual category for display
                $jobs = JobListing::with(['company', 'categories'])
                    ->where('status', 'published')
                    ->whereHas('categories', function($query) use ($categoryName) {
                        $query->where('name', 'LIKE', "%{$categoryName}%");
                    })
                    ->orWhere('title', 'LIKE', "%{$categoryName}%")
                    ->orWhere('description', 'LIKE', "%{$categoryName}%")
                    ->latest()
                    ->paginate(20);

                $virtualCategory = (object) [
                    'name' => $categoryName,
                    'slug' => $slug,
                    'description' => "Find the best {$categoryName} jobs and career opportunities."
                ];

                $theme = get_theme();
                return view("frontend.{$theme}.category-jobs", compact('category', 'jobs'))
                    ->with('category', $virtualCategory);
            }
        }

        // Handle service pages (training, recruitment, careers, etc.)
        $servicePages = [
            'training', 'recruitment', 'careers', 'courses', 'institutes',
            'professionals', 'work_shop', 'certification', 'contact', 'faq',
            'it_careers', 'non_it_careers', 'overseas', 'counselling', 'service_advice'
        ];

        if (in_array($slug, $servicePages)) {
            $theme = get_theme();
            $viewPath = "frontend.{$theme}.{$slug}";

            if (view()->exists($viewPath)) {
                return view($viewPath);
            }
        }

        // Handle resume and cover letter pages
        $templatePages = [
            'service_templates', 'cover_letter', 'it_resume_main', 'non_it_resume_main',
            'cover_letter_main', 'it_resume_web', 'it_resume_android', 'it_resume_manager',
            'cover_letter_it', 'cover_letter_banking', 'cover_letter_hr'
        ];

        if (in_array($slug, $templatePages)) {
            $theme = get_theme();
            $viewPath = "frontend.{$theme}.{$slug}";

            if (view()->exists($viewPath)) {
                return view($viewPath);
            }
        }

        // Legacy redirect handler for old static routes
        $redirectMap = [
            'index1' => 'home',
            'search_page' => 'jobs.search',
            'jobs' => 'jobs.all',
            'companies' => 'frontend.companies',
        ];

        if (isset($redirectMap[$slug])) {
            return redirect()->route($redirectMap[$slug]);
        }

        // Try to find the page in the dynamic Page system
        $pageModel = Page::active()->where('slug', $slug)->first();
        if ($pageModel) {
            return redirect()->route('page.show', $slug);
        }

        // If page not found, redirect to home with a message
        return redirect()->route('home')->with('info', 'The requested page has been moved or no longer exists.');
    }
}
