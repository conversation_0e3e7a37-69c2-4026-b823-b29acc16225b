@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and ensure Tailwind takes precedence */
* {
    box-sizing: border-box;
}

/* Ensure proper spacing and layout */
body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Custom gradient classes */
.gradient-bg {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #dc2626 100%);
}

.red-gradient {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}


