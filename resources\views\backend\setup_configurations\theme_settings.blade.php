<x-app-backend-layout>
    <div class="max-w-7xl mx-auto px-4 py-6">
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Theme Settings') }}</h4>
            
            <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-yellow-700">
                    <i class="fas fa-info-circle mr-2"></i>
                    {{ __('Changing themes requires rebuilding assets with "npm run build" to take full effect. The settings will be updated immediately, but visual changes may not appear until assets are rebuilt.') }}
                </p>
            </div>
            
            <form action="{{ route('update_theme') }}" method="POST">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($themes as $theme_id => $theme_name)
                        <div class="border border-gray-200 rounded-lg overflow-hidden transition-shadow hover:shadow-md p-4 {{ $active_theme == $theme_id ? 'ring-2 ring-blue-500' : '' }}">
                            <div class="flex items-center justify-between mb-2">
                                <h5 class="text-lg font-semibold">{{ $theme_name }}</h5>
                                <div class="flex items-center">
                                    <input type="radio" name="theme" id="theme_{{ $theme_id }}" value="{{ $theme_id }}" 
                                           class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                           {{ $active_theme == $theme_id ? 'checked' : '' }}>
                                    <label for="theme_{{ $theme_id }}" class="ml-2 text-sm font-medium text-gray-700">
                                        {{ $active_theme == $theme_id ? __('Active') : __('Select') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="flex items-center mt-4">
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs mr-2">
                                    {{ __('Directory') }}: resources/views/front/{{ $theme_id }}
                                </span>
                                <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                                    {{ __('Assets') }}: resources/{css,js}/frontend/{{ $theme_id }}
                                </span>
                            </div>
                        </div>
                    @endforeach
                </div>
                
                <div class="mt-6 flex justify-between items-center">
                    <span class="text-sm text-gray-500">
                        {{ __('Current theme') }}: <strong>{{ $themes[$active_theme] }}</strong>
                    </span>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        {{ __('Update Theme') }}
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Tailwind Theme Configuration Section (Moved from activation page) -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Tailwind Theme Configuration') }}</h4>
        
            <form action="{{ route('business_settings.tailwind_config_update') }}" method="POST">
                @csrf
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Text Color -->
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <label class="block mb-2 text-base font-medium text-gray-700">{{ __('Text Color') }}</label>
                        <div x-data="{ colorValue: '{{ $tailwind_colors['text'] ?? '#333333' }}' }" class="flex items-center space-x-3">
                            <input type="color" id="text_color" name="colors[text]" 
                                   x-model="colorValue"
                                   class="h-10 w-12 border-0 p-0 rounded cursor-pointer">
                            <input type="text" 
                                   x-model="colorValue"
                                   name="colors[text]" 
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Default color for body text across the site') }}</p>
                    </div>
                    
                    <!-- Background Color -->
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <label class="block mb-2 text-base font-medium text-gray-700">{{ __('Background Color') }}</label>
                        <div x-data="{ colorValue: '{{ $tailwind_colors['background'] ?? '#ffffff' }}' }" class="flex items-center space-x-3">
                            <input type="color" id="background_color" name="colors[background]" 
                                   x-model="colorValue"
                                   class="h-10 w-12 border-0 p-0 rounded cursor-pointer">
                            <input type="text" 
                                   x-model="colorValue"
                                   name="colors[background]" 
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Main background color for the site layout') }}</p>
                    </div>
                    
                    <!-- Primary Color -->
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <label class="block mb-2 text-base font-medium text-gray-700">{{ __('Primary Color') }}</label>
                        <div x-data="{ colorValue: '{{ $tailwind_colors['primary'] ?? '#3490dc' }}' }" class="flex items-center space-x-3">
                            <input type="color" id="primary_color" name="colors[primary]" 
                                   x-model="colorValue"
                                   class="h-10 w-12 border-0 p-0 rounded cursor-pointer">
                            <input type="text" 
                                   x-model="colorValue"
                                   name="colors[primary]" 
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Main brand color used for buttons, links, and primary actions') }}</p>
                    </div>
        
                    <!-- Secondary Color -->
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <label class="block mb-2 text-base font-medium text-gray-700">{{ __('Secondary Color') }}</label>
                        <div x-data="{ colorValue: '{{ $tailwind_colors['secondary'] ?? '#ffed4a' }}' }" class="flex items-center space-x-3">
                            <input type="color" id="secondary_color" name="colors[secondary]" 
                                   x-model="colorValue"
                                   class="h-10 w-12 border-0 p-0 rounded cursor-pointer">
                            <input type="text" 
                                   x-model="colorValue"
                                   name="colors[secondary]" 
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Complementary color for secondary elements and accents') }}</p>
                    </div>
                    
                    <!-- Accent Color -->
                    <div class="p-4 border border-gray-200 rounded-lg">
                        <label class="block mb-2 text-base font-medium text-gray-700">{{ __('Accent Color') }}</label>
                        <div x-data="{ colorValue: '{{ $tailwind_colors['accent'] ?? '#f6993f' }}' }" class="flex items-center space-x-3">
                            <input type="color" id="accent_color" name="colors[accent]" 
                                   x-model="colorValue"
                                   class="h-10 w-12 border-0 p-0 rounded cursor-pointer">
                            <input type="text" 
                                   x-model="colorValue"
                                   name="colors[accent]" 
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
                        </div>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Eye-catching color for highlights and attention points') }}</p>
                    </div>
                </div>
        
                <div class="flex items-center justify-between mt-8">
                    <div class="text-sm text-blue-700 bg-blue-50 p-3 rounded-md max-w-lg">
                        {{ __('Changing these colors will modify your entire site\'s theme. Changes will take effect after the Tailwind styles are rebuilt.') }}
                    </div>
                    <button type="submit" class="px-5 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300">
                        {{ __('Save Colors') }}
                    </button>
                </div>
            </form>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Manual Theme Build') }}</h4>
            
            <p class="mb-4 text-gray-600">
                {{ __('If theme changes are not visible after switching, run the build command to compile the assets for the active theme.') }}
            </p>
            
            <div class="bg-gray-50 p-4 rounded-lg font-mono text-sm">
                <p class="mb-2 text-gray-700">{{ __('Run this command in your project root:') }}</p>
                <code class="block bg-gray-800 text-white p-3 rounded overflow-x-auto">npm run build</code>
            </div>
        </div>
    </div>

@if(session('success'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Toastify({
            text: "{{ session('success') }}",
            duration: 3000,
            close: true,
            backgroundColor: "#10b981",
        }).showToast();
    });
</script>
@endif

</x-app-backend-layout>