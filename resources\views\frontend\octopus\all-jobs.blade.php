@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">All Jobs</h1>
                <p class="text-white link-nav">
                    <a href="{{url('/')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="{{url('/jobs')}}">All Jobs</a>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start Job Listings Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 post-list">
                <h2 class="text-center mb-4">All Available Jobs</h2>
                
                @if($jobs->count() > 0)
                    @foreach($jobs as $job)
                        <div class="single-post d-flex flex-row">
                            <div class="details col-lg-12">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}">
                                            <h4>{{ $job->title }}</h4>
                                        </a>
                                        <h6>{{ $job->company->name ?? 'Company' }}</h6>
                                        <p>{{ $job->location }}</p>
                                    </div>
                                    <div class="btns">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}" class="btn btn-primary">View Details</a>
                                    </div>
                                </div>
                                <p>{{ Str::limit($job->description, 200) }}</p>
                                <h5>
                                    Job Type: <span>{{ ucfirst($job->job_type) }}</span><br>
                                    Experience: <span>{{ $job->experience_level }}</span><br>
                                    @if($job->salary_min && $job->salary_max)
                                        Salary: <span>${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}</span>
                                    @endif
                                </h5>
                                <p class="address">
                                    <span class="lnr lnr-map"></span> {{ $job->location }}
                                    <span class="lnr lnr-database"></span> {{ $job->created_at->diffForHumans() }}
                                </p>
                            </div>
                        </div>
                    @endforeach
                    
                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            {{ $jobs->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No jobs found</h4>
                        <p>Please check back later for new opportunities.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End Job Listings Area -->
@endsection
