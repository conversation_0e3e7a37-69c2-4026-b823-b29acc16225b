@extends('frontend.layouts.app')

@section('title', $category->name . ' Jobs | JobOctopus')
@section('meta_description', 'Find ' . $category->name . ' jobs and career opportunities. Browse latest job openings in ' . $category->name . ' sector.')
@section('meta_keywords', $category->name . ', jobs, career, employment, opportunities')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    {{ $category->name }} Jobs
                </h1>
                <p class="text-xl text-white opacity-90 max-w-3xl mx-auto mb-4">
                    Discover exciting career opportunities in {{ $category->name }}. Find your perfect job match today.
                </p>
                <div class="flex flex-wrap justify-center gap-4 mt-6">
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-white bg-white bg-opacity-20">
                        <i class="fa fa-briefcase mr-2"></i>
                        {{ $jobs->total() }} Jobs Available
                    </span>
                    @if($jobs->hasPages())
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-white bg-white bg-opacity-20">
                            <i class="fa fa-file-text-o mr-2"></i>
                            Page {{ $jobs->currentPage() }} of {{ $jobs->lastPage() }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Search Form -->
    <section class="py-8 bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <form action="{{ route('jobs.search') }}" method="GET" class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 shadow-lg border border-gray-200">
                <!-- Hidden category field to maintain category filter -->
                <input type="hidden" name="category" value="{{ $category->id }}">

                <!-- Basic Search Row -->
                <div class="grid grid-cols-1 md:grid-cols-12 gap-4 mb-6">
                    <div class="md:col-span-5">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fa fa-search mr-1"></i>
                            Keywords, Skills, or Company
                        </label>
                        <input
                            type="text"
                            name="keywords"
                            placeholder="e.g. Software Developer, Marketing, Google"
                            class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900 placeholder-gray-500"
                        >
                    </div>
                    <div class="md:col-span-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fa fa-map-marker mr-1"></i>
                            Location
                        </label>
                        <input
                            type="text"
                            name="location"
                            placeholder="City, State, or Remote"
                            class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900 placeholder-gray-500"
                        >
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fa fa-sort mr-1"></i>
                            Sort By
                        </label>
                        <select name="sort_by" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900">
                            <option value="latest">Latest</option>
                            <option value="salary_high">Salary: High to Low</option>
                            <option value="salary_low">Salary: Low to High</option>
                            <option value="oldest">Oldest First</option>
                        </select>
                    </div>
                    <div class="md:col-span-2 flex items-end">
                        <button type="submit" class="w-full red-gradient text-white rounded-lg py-3 px-6 font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                            <i class="fa fa-search mr-2"></i>
                            Search Jobs
                        </button>
                    </div>
                </div>

                <!-- Category Info -->
                <div class="border-t border-gray-200 pt-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-700 mr-3">Searching in:</span>
                            <span class="px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg text-sm font-semibold">
                                <i class="fa fa-tag mr-1"></i>
                                {{ $category->name }}
                            </span>
                        </div>
                        <a href="{{ route('jobs.search') }}" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            Search all categories
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <!-- Jobs Listing -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($jobs->count() > 0)
                <!-- Results Summary -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8 p-6 bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="mb-4 md:mb-0">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">
                            {{ $jobs->total() }} {{ $category->name }} Jobs Found
                        </h2>
                        <p class="text-gray-600">
                            Career opportunities in {{ $category->name }}
                        </p>
                    </div>
                    <div class="flex items-center gap-4">
                        <span class="text-sm text-gray-500">
                            Showing {{ $jobs->firstItem() }}-{{ $jobs->lastItem() }} of {{ $jobs->total() }}
                        </span>
                    </div>
                </div>

                <div class="space-y-6">
                    @foreach($jobs as $job)
                        <div class="transition-all duration-300 hover:-translate-y-0.5 hover:shadow-xl bg-white rounded-xl shadow-sm border border-gray-200 relative overflow-hidden group">
                            <!-- Decorative gradient line -->
                            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-br from-blue-500 via-blue-700 to-red-600"></div>

                            <!-- Featured Job Badge -->
                            @if($job->is_featured)
                                <div class="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                    <i class="fa fa-star mr-1"></i>
                                    Featured
                                </div>
                            @endif

                            <div class="p-6">
                                <div class="flex items-start gap-4 mb-4">
                                    <!-- Company Logo -->
                                    <div class="flex-shrink-0">
                                        @if($job->company && $job->company->logo)
                                            <img src="{{url('storage/' . $job->company->logo)}}" alt="{{$job->company->name}}" class="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white p-2">
                                        @else
                                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                <i class="fa fa-building text-white text-xl"></i>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Job Details -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex-1 min-w-0">
                                                <h3 class="text-xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors group-hover:text-blue-600">
                                                    <a href="{{ route('frontend.job.show', $job->slug) }}" class="block">{{$job->title}}</a>
                                                </h3>
                                                <p class="text-gray-600 font-semibold text-base mb-1">{{$job->company->name ?? 'Company Name'}}</p>
                                                <p class="text-sm text-blue-600 font-medium">
                                                    {{ $category->name }}
                                                </p>
                                            </div>
                                            <!-- Posted Time & Remote Badge -->
                                            <div class="flex-shrink-0 ml-4 text-right">
                                                <span class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-md block mb-2">{{$job->created_at->diffForHumans()}}</span>
                                                @if($job->remote_option)
                                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                                        <i class="fa fa-home mr-1"></i>
                                                        Remote
                                                    </span>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Job Meta Info -->
                                        <div class="flex flex-wrap gap-2 text-sm text-gray-500 mb-4">
                                            @if($job->experience_level)
                                                <span class="flex items-center bg-gray-100 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-briefcase mr-1.5 text-gray-400"></i>
                                                    {{$job->experience_level}}
                                                </span>
                                            @endif
                                            @if($job->location)
                                                <span class="flex items-center bg-gray-100 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-map-marker mr-1.5 text-gray-400"></i>
                                                    {{$job->location}}
                                                </span>
                                            @endif
                                            @if($job->job_type)
                                                <span class="flex items-center bg-gray-100 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-clock-o mr-1.5 text-gray-400"></i>
                                                    {{ucfirst(str_replace('-', ' ', $job->job_type))}}
                                                </span>
                                            @endif
                                            @if($job->application_deadline)
                                                <span class="flex items-center bg-red-100 text-red-700 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-calendar mr-1.5"></i>
                                                    Deadline: {{ $job->application_deadline->format('M d, Y') }}
                                                </span>
                                            @endif
                                        </div>

                                        @if($job->salary_min && $job->salary_max)
                                            <div class="mb-4">
                                                <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-sm">
                                                    <i class="fa fa-money mr-2"></i>
                                                    ₹{{number_format($job->salary_min)}} - ₹{{number_format($job->salary_max)}}
                                                    @if($job->salary_period)
                                                        <span class="ml-1 text-xs opacity-90">/ {{ $job->salary_period }}</span>
                                                    @endif
                                                </span>
                                            </div>
                                        @endif

                                        @if($job->skills_required && is_array($job->skills_required))
                                            <div class="mb-4">
                                                <p class="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">Required Skills:</p>
                                                <div class="flex flex-wrap gap-2">
                                                    @foreach(array_slice($job->skills_required, 0, 5) as $skill)
                                                        <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-3 py-1.5 bg-blue-50 text-blue-700 text-xs rounded-lg border border-blue-200 font-semibold hover:bg-blue-100">{{$skill}}</span>
                                                    @endforeach
                                                    @if(count($job->skills_required) > 5)
                                                        <span class="px-3 py-1.5 bg-gray-100 text-gray-600 text-xs rounded-lg font-semibold">+{{count($job->skills_required) - 5}} more</span>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif

                                        @if($job->description)
                                            <p class="text-gray-600 text-sm leading-relaxed mb-4">
                                                {{Str::limit(strip_tags($job->description), 150)}}
                                            </p>
                                        @endif
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                    <div class="flex items-center gap-3">
                                        <a href="{{ route('frontend.job.show', $job->slug) }}" class="red-gradient text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 text-sm font-bold transform hover:scale-105 inline-block">
                                            <i class="fa fa-paper-plane mr-2"></i>
                                            Apply Now
                                        </a>
                                        <button class="border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-semibold">
                                            <i class="fa fa-bookmark-o mr-1.5"></i>
                                            Save Job
                                        </button>
                                        <button class="border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-semibold">
                                            <i class="fa fa-share mr-1.5"></i>
                                            Share
                                        </button>
                                    </div>
                                    <div class="flex items-center gap-4 text-sm text-gray-400">
                                        <button class="hover:text-red-500 transition-colors p-2 rounded-lg hover:bg-red-50">
                                            <i class="fa fa-heart-o text-base"></i>
                                        </button>
                                        <span class="flex items-center bg-gray-50 px-3 py-2 rounded-lg">
                                            <i class="fa fa-eye mr-1.5"></i>
                                            {{$job->views_count ?? 0}} views
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Enhanced Pagination -->
                <div class="mt-12 flex flex-col items-center">
                    <div class="mb-4">
                        {{ $jobs->links() }}
                    </div>
                    <p class="text-sm text-gray-600">
                        Showing {{ $jobs->firstItem() }} to {{ $jobs->lastItem() }} of {{ $jobs->total() }} results
                    </p>
                </div>
            @else
                <!-- Enhanced No Results Section -->
                <div class="text-center py-20">
                    <div class="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                        <i class="fa fa-tag text-gray-400 text-4xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No {{ $category->name }} Jobs Found</h3>
                    <p class="text-gray-600 mb-8 max-w-md mx-auto">
                        We couldn't find any jobs in the {{ $category->name }} category at the moment. Try checking other categories or set up job alerts.
                    </p>

                    <!-- Suggestions -->
                    <div class="bg-blue-50 rounded-xl p-6 max-w-lg mx-auto mb-8">
                        <h4 class="font-semibold text-gray-900 mb-3">What you can do:</h4>
                        <ul class="text-sm text-gray-600 space-y-2 text-left">
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Set up job alerts for {{ $category->name }} positions
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Browse related categories
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Search all job categories
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Check back regularly for new postings
                            </li>
                        </ul>
                    </div>

                    <div class="flex flex-wrap justify-center gap-4">
                        <a href="{{ route('jobs.search') }}" class="red-gradient text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 font-semibold">
                            <i class="fa fa-search mr-2"></i>
                            Browse All Jobs
                        </a>
                        <a href="{{ route('home') }}" class="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-300 transition-all duration-300 font-semibold">
                            <i class="fa fa-home mr-2"></i>
                            Go to Homepage
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
</div>
@endsection
