@extends('backend.layouts.app')

@section('title', 'Google Firebase Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Google Firebase Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('business_settings.update') }}" method="POST">
                        @csrf
                        <input type="hidden" name="types[]" value="firebase_status">
                        <input type="hidden" name="types[]" value="firebase_api_key">
                        <input type="hidden" name="types[]" value="firebase_auth_domain">
                        <input type="hidden" name="types[]" value="firebase_project_id">
                        <input type="hidden" name="types[]" value="firebase_storage_bucket">
                        <input type="hidden" name="types[]" value="firebase_messaging_sender_id">
                        <input type="hidden" name="types[]" value="firebase_app_id">

                        <div class="form-group">
                            <label class="form-label">Enable Firebase</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="firebase_status" 
                                    {{ get_setting('firebase_status') == 1 ? 'checked' : '' }}>
                                <label class="form-check-label">Enable Firebase integration</label>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firebase_api_key">API Key</label>
                                    <input type="text" name="firebase_api_key" class="form-control" 
                                        value="{{ get_setting('firebase_api_key') }}" 
                                        placeholder="Enter Firebase API Key">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firebase_auth_domain">Auth Domain</label>
                                    <input type="text" name="firebase_auth_domain" class="form-control" 
                                        value="{{ get_setting('firebase_auth_domain') }}" 
                                        placeholder="your-project.firebaseapp.com">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firebase_project_id">Project ID</label>
                                    <input type="text" name="firebase_project_id" class="form-control" 
                                        value="{{ get_setting('firebase_project_id') }}" 
                                        placeholder="your-project-id">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firebase_storage_bucket">Storage Bucket</label>
                                    <input type="text" name="firebase_storage_bucket" class="form-control" 
                                        value="{{ get_setting('firebase_storage_bucket') }}" 
                                        placeholder="your-project.appspot.com">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firebase_messaging_sender_id">Messaging Sender ID</label>
                                    <input type="text" name="firebase_messaging_sender_id" class="form-control" 
                                        value="{{ get_setting('firebase_messaging_sender_id') }}" 
                                        placeholder="123456789012">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="firebase_app_id">App ID</label>
                                    <input type="text" name="firebase_app_id" class="form-control" 
                                        value="{{ get_setting('firebase_app_id') }}" 
                                        placeholder="1:123456789012:web:abcdef123456">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Firebase Settings</button>
                        </div>
                    </form>

                    <!-- Setup Instructions -->
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Setup Instructions</h5>
                            <ol>
                                <li>Go to <a href="https://console.firebase.google.com/" target="_blank">Firebase Console</a></li>
                                <li>Create a new project or select an existing one</li>
                                <li>Add a web app to your project</li>
                                <li>Copy the configuration values from the Firebase SDK setup</li>
                                <li>Paste the values in the fields above</li>
                                <li>Enable the Firebase services you need:
                                    <ul>
                                        <li>Authentication</li>
                                        <li>Cloud Messaging</li>
                                        <li>Analytics</li>
                                        <li>Storage</li>
                                    </ul>
                                </li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Current Status</h5>
                            <div class="alert {{ get_setting('firebase_status') == 1 ? 'alert-success' : 'alert-warning' }}">
                                Firebase is currently 
                                <strong>{{ get_setting('firebase_status') == 1 ? 'Enabled' : 'Disabled' }}</strong>
                            </div>
                            
                            @if(get_setting('firebase_project_id'))
                                <div class="alert alert-info">
                                    <strong>Project Configured:</strong><br>
                                    Project ID: {{ get_setting('firebase_project_id') }}
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <strong>No Project Configured</strong><br>
                                    Please configure your Firebase project settings.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Firebase Services -->
                    <hr>
                    <h5>Firebase Services</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-user-shield fa-3x text-primary mb-3"></i>
                                    <h6>Authentication</h6>
                                    <p class="text-muted">User authentication and management</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-bell fa-3x text-success mb-3"></i>
                                    <h6>Cloud Messaging</h6>
                                    <p class="text-muted">Push notifications to users</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-bar fa-3x text-warning mb-3"></i>
                                    <h6>Analytics</h6>
                                    <p class="text-muted">App usage analytics and insights</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-cloud fa-3x text-info mb-3"></i>
                                    <h6>Storage</h6>
                                    <p class="text-muted">File storage and management</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
