@extends('backend.layouts.app')

@section('title', 'Payment Methods')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Payment Method Configuration</h4>
                </div>
                <div class="card-body">
                    @if(isset($payment_methods) && $payment_methods->count() > 0)
                        <div class="row">
                            @foreach($payment_methods as $payment_method)
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-3">
                                                @if($payment_method->image)
                                                    <img src="{{ asset($payment_method->image) }}" 
                                                        alt="{{ $payment_method->name }}" 
                                                        class="me-3" style="height: 40px;">
                                                @else
                                                    <i class="fas fa-credit-card fa-2x text-primary me-3"></i>
                                                @endif
                                                <div>
                                                    <h5 class="card-title mb-0">{{ $payment_method->name }}</h5>
                                                    <small class="text-muted">{{ ucfirst($payment_method->type) }}</small>
                                                </div>
                                            </div>
                                            
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input payment-toggle" 
                                                    type="checkbox" 
                                                    data-method="{{ $payment_method->type }}"
                                                    {{ get_setting($payment_method->type . '_activation') == 1 ? 'checked' : '' }}>
                                                <label class="form-check-label">
                                                    {{ get_setting($payment_method->type . '_activation') == 1 ? 'Enabled' : 'Disabled' }}
                                                </label>
                                            </div>
                                            
                                            @if($payment_method->type == 'stripe')
                                                <button type="button" class="btn btn-primary btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#stripeModal">
                                                    Configure Stripe
                                                </button>
                                            @elseif($payment_method->type == 'paypal')
                                                <button type="button" class="btn btn-primary btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#paypalModal">
                                                    Configure PayPal
                                                </button>
                                            @elseif($payment_method->type == 'razorpay')
                                                <button type="button" class="btn btn-primary btn-sm" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#razorpayModal">
                                                    Configure Razorpay
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="alert alert-info">
                            <h5>No Payment Methods Found</h5>
                            <p>Payment methods will be displayed here once they are configured in the system.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stripe Configuration Modal -->
<div class="modal fade" id="stripeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Stripe Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('business_settings.update') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="types[]" value="STRIPE_KEY">
                    <input type="hidden" name="types[]" value="STRIPE_SECRET">
                    
                    <div class="form-group mb-3">
                        <label for="STRIPE_KEY">Stripe Publishable Key</label>
                        <input type="text" name="STRIPE_KEY" class="form-control" 
                            value="{{ env('STRIPE_KEY') }}" 
                            placeholder="pk_test_...">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="STRIPE_SECRET">Stripe Secret Key</label>
                        <input type="password" name="STRIPE_SECRET" class="form-control" 
                            value="{{ env('STRIPE_SECRET') }}" 
                            placeholder="sk_test_...">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="stripe_sandbox" 
                            {{ get_setting('stripe_sandbox') == 1 ? 'checked' : '' }}>
                        <label class="form-check-label">Sandbox Mode</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Configuration</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- PayPal Configuration Modal -->
<div class="modal fade" id="paypalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">PayPal Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('business_settings.update') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="types[]" value="PAYPAL_CLIENT_ID">
                    <input type="hidden" name="types[]" value="PAYPAL_CLIENT_SECRET">
                    
                    <div class="form-group mb-3">
                        <label for="PAYPAL_CLIENT_ID">PayPal Client ID</label>
                        <input type="text" name="PAYPAL_CLIENT_ID" class="form-control" 
                            value="{{ env('PAYPAL_CLIENT_ID') }}" 
                            placeholder="Your PayPal Client ID">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="PAYPAL_CLIENT_SECRET">PayPal Client Secret</label>
                        <input type="password" name="PAYPAL_CLIENT_SECRET" class="form-control" 
                            value="{{ env('PAYPAL_CLIENT_SECRET') }}" 
                            placeholder="Your PayPal Client Secret">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="paypal_sandbox" 
                            {{ get_setting('paypal_sandbox') == 1 ? 'checked' : '' }}>
                        <label class="form-check-label">Sandbox Mode</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Configuration</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Razorpay Configuration Modal -->
<div class="modal fade" id="razorpayModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Razorpay Configuration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('business_settings.update') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <input type="hidden" name="types[]" value="RAZORPAY_KEY">
                    <input type="hidden" name="types[]" value="RAZORPAY_SECRET">
                    
                    <div class="form-group mb-3">
                        <label for="RAZORPAY_KEY">Razorpay Key ID</label>
                        <input type="text" name="RAZORPAY_KEY" class="form-control" 
                            value="{{ env('RAZORPAY_KEY') }}" 
                            placeholder="rzp_test_...">
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="RAZORPAY_SECRET">Razorpay Key Secret</label>
                        <input type="password" name="RAZORPAY_SECRET" class="form-control" 
                            value="{{ env('RAZORPAY_SECRET') }}" 
                            placeholder="Your Razorpay Secret">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Configuration</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggles = document.querySelectorAll('.payment-toggle');
    
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const method = this.dataset.method;
            const value = this.checked ? 1 : 0;
            
            fetch('{{ route("payment.activation") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    type: method + '_activation',
                    value: value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const label = this.nextElementSibling;
                    label.textContent = this.checked ? 'Enabled' : 'Disabled';
                }
            });
        });
    });
});
</script>
@endsection
