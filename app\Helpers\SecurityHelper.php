<?php

namespace App\Helpers;

class SecurityHelper
{
    /**
     * Sanitize string input to prevent XSS and injection attacks
     */
    public static function sanitizeString(string $input, bool $allowHtml = false): string
    {
        // Trim whitespace
        $input = trim($input);
        
        if (!$allowHtml) {
            // Strip all HTML tags
            $input = strip_tags($input);
        } else {
            // Allow only safe HTML tags
            $allowedTags = '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>';
            $input = strip_tags($input, $allowedTags);
        }
        
        // Convert special characters to HTML entities
        $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        return $input;
    }

    /**
     * Sanitize search query to prevent SQL injection
     */
    public static function sanitizeSearchQuery(string $query): string
    {
        // Remove potentially dangerous characters
        $query = preg_replace('/[^\w\s\-\.\@]/', '', $query);
        
        // Limit length
        $query = substr($query, 0, 255);
        
        // Trim and normalize whitespace
        $query = preg_replace('/\s+/', ' ', trim($query));
        
        return $query;
    }

    /**
     * Validate and sanitize email address
     */
    public static function sanitizeEmail(string $email): string
    {
        $email = trim(strtolower($email));
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('Invalid email address');
        }
        
        return $email;
    }

    /**
     * Sanitize phone number
     */
    public static function sanitizePhone(string $phone): string
    {
        // Remove all non-numeric characters except + and -
        $phone = preg_replace('/[^\d\+\-\(\)\s]/', '', $phone);
        
        // Trim whitespace
        $phone = trim($phone);
        
        return $phone;
    }

    /**
     * Sanitize URL
     */
    public static function sanitizeUrl(string $url): string
    {
        $url = trim($url);
        $url = filter_var($url, FILTER_SANITIZE_URL);
        
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new \InvalidArgumentException('Invalid URL');
        }
        
        // Ensure HTTPS for external URLs
        if (!str_starts_with($url, 'http://localhost') && !str_starts_with($url, 'https://')) {
            $url = 'https://' . ltrim($url, 'http://');
        }
        
        return $url;
    }

    /**
     * Sanitize filename for safe storage
     */
    public static function sanitizeFilename(string $filename): string
    {
        // Get file extension
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $basename = pathinfo($filename, PATHINFO_FILENAME);
        
        // Remove dangerous characters
        $basename = preg_replace('/[^a-zA-Z0-9\-_]/', '_', $basename);
        
        // Limit length
        $basename = substr($basename, 0, 100);
        
        // Add timestamp to prevent conflicts
        $basename = $basename . '_' . time();
        
        return $basename . '.' . $extension;
    }

    /**
     * Check if string contains suspicious patterns
     */
    public static function containsSuspiciousPatterns(string $input): bool
    {
        $suspiciousPatterns = [
            '/\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/i',
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i',
            '/onclick=/i',
            '/onmouseover=/i',
            '/eval\s*\(/i',
            '/expression\s*\(/i',
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Generate secure random token
     */
    public static function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Hash sensitive data
     */
    public static function hashSensitiveData(string $data): string
    {
        return hash('sha256', $data . config('app.key'));
    }

    /**
     * Validate CSRF token
     */
    public static function validateCsrfToken(string $token): bool
    {
        return hash_equals(session()->token(), $token);
    }

    /**
     * Rate limit check
     */
    public static function checkRateLimit(string $key, int $maxAttempts = 60, int $decayMinutes = 1): bool
    {
        $attempts = cache()->get($key, 0);
        
        if ($attempts >= $maxAttempts) {
            return false;
        }
        
        cache()->put($key, $attempts + 1, now()->addMinutes($decayMinutes));
        
        return true;
    }

    /**
     * Log security event
     */
    public static function logSecurityEvent(string $event, array $context = []): void
    {
        \Log::channel('security')->warning($event, array_merge($context, [
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'user_id' => auth()->id(),
            'timestamp' => now()->toISOString(),
        ]));
    }
}
