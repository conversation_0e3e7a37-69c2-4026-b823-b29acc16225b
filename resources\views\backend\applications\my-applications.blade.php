@extends('backend.layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">My Applications</h1>

            @if($applications->count())
                <div class="space-y-4">
                    @foreach($applications as $application)
                        <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition duration-200">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-start justify-between">
                                        <div>
                                            <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                                <a href="{{ route('jobs-listing.show', $application->jobListing) }}"
                                                   class="hover:text-blue-600 transition duration-200">
                                                    {{ $application->jobListing->title }}
                                                </a>
                                            </h3>
                                            <p class="text-gray-600 mb-2">
                                                <span class="font-medium">{{ $application->jobListing->company->name }}</span>
                                                • {{ $application->jobListing->location }}
                                            </p>
                                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                                <span>Applied {{ time_ago($application->created_at) }}</span>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ get_job_type_badge($application->jobListing->job_type) }}">
                                                    {{ ucfirst($application->jobListing->job_type) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ get_status_badge($application->status) }}">
                                                {{ ucfirst($application->status) }}
                                            </span>
                                            <a href="{{ route('applications.show', $application) }}"
                                               class="text-blue-600 hover:text-blue-800 font-medium">
                                                View Details
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Job Description Preview -->
                                    <div class="mt-4">
                                        <p class="text-gray-700 text-sm">
                                            {{ truncate_text($application->jobListing->description, 200) }}
                                        </p>
                                    </div>

                                    <!-- Salary Information -->
                                    @if($application->jobListing->salary_min && $application->jobListing->salary_max)
                                        <div class="mt-3">
                                            <span class="text-sm text-gray-600">Salary: </span>
                                            <span class="text-sm font-medium text-gray-900">
                                                {{ format_currency($application->jobListing->salary_min, $application->jobListing->salary_currency) }} -
                                                {{ format_currency($application->jobListing->salary_max, $application->jobListing->salary_currency) }}
                                                @if($application->jobListing->salary_period)
                                                    per {{ $application->jobListing->salary_period }}
                                                @endif
                                            </span>
                                        </div>
                                    @endif

                                    <!-- Application Deadline -->
                                    @if($application->jobListing->application_deadline)
                                        <div class="mt-2">
                                            <span class="text-sm text-gray-600">Deadline: </span>
                                            <span class="text-sm font-medium text-gray-900">
                                                {{ $application->jobListing->application_deadline->format('F j, Y') }}
                                            </span>
                                            @if($application->jobListing->application_deadline->isPast())
                                                <span class="text-red-500 text-xs ml-1">(Expired)</span>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-4 pt-4 border-t border-gray-200 flex justify-between items-center">
                                <div class="flex space-x-3">
                                    <a href="{{ route('jobs-listing.show', $application->jobListing) }}"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        View Job
                                    </a>
                                    <a href="{{ route('applications.show', $application) }}"
                                       class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        View Application
                                    </a>
                                    @if($application->jobListing->company->email)
                                        <a href="mailto:{{ $application->jobListing->company->email }}"
                                           class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                            Contact Company
                                        </a>
                                    @endif
                                </div>

                                <!-- Status-specific actions -->
                                @if($application->status === 'pending')
                                    <span class="text-sm text-gray-500">Waiting for review</span>
                                @elseif($application->status === 'reviewing')
                                    <span class="text-sm text-blue-600">Under review</span>
                                @elseif($application->status === 'shortlisted')
                                    <span class="text-sm text-green-600 font-medium">Shortlisted! 🎉</span>
                                @elseif($application->status === 'rejected')
                                    <span class="text-sm text-red-600">Not selected</span>
                                @elseif($application->status === 'hired')
                                    <span class="text-sm text-purple-600 font-medium">Congratulations! 🎉</span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    {{ $applications->links() }}
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No applications yet</h3>
                    <p class="mt-1 text-sm text-gray-500">Start applying for jobs to see your applications here.</p>
                    <div class="mt-6">
                        <a href="{{ route('jobs-listing.index') }}"
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Browse Jobs
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
