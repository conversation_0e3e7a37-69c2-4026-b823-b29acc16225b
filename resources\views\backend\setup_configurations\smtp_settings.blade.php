@extends('backend.layouts.app')

@section('title', 'SMTP Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">SMTP Settings</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('business_settings.update') }}" method="POST">
                        @csrf
                        <input type="hidden" name="types[]" value="MAIL_DRIVER">
                        <input type="hidden" name="types[]" value="MAIL_HOST">
                        <input type="hidden" name="types[]" value="MAIL_PORT">
                        <input type="hidden" name="types[]" value="MAIL_USERNAME">
                        <input type="hidden" name="types[]" value="MAIL_PASSWORD">
                        <input type="hidden" name="types[]" value="MAIL_ENCRYPTION">
                        <input type="hidden" name="types[]" value="MAIL_FROM_ADDRESS">
                        <input type="hidden" name="types[]" value="MAIL_FROM_NAME">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_DRIVER">Mail Driver</label>
                                    <select name="MAIL_DRIVER" class="form-control">
                                        <option value="smtp" {{ env('MAIL_DRIVER') == 'smtp' ? 'selected' : '' }}>SMTP</option>
                                        <option value="sendmail" {{ env('MAIL_DRIVER') == 'sendmail' ? 'selected' : '' }}>Sendmail</option>
                                        <option value="mailgun" {{ env('MAIL_DRIVER') == 'mailgun' ? 'selected' : '' }}>Mailgun</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_HOST">Mail Host</label>
                                    <input type="text" name="MAIL_HOST" class="form-control" value="{{ env('MAIL_HOST') }}" placeholder="smtp.gmail.com">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_PORT">Mail Port</label>
                                    <input type="number" name="MAIL_PORT" class="form-control" value="{{ env('MAIL_PORT') }}" placeholder="587">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_ENCRYPTION">Mail Encryption</label>
                                    <select name="MAIL_ENCRYPTION" class="form-control">
                                        <option value="tls" {{ env('MAIL_ENCRYPTION') == 'tls' ? 'selected' : '' }}>TLS</option>
                                        <option value="ssl" {{ env('MAIL_ENCRYPTION') == 'ssl' ? 'selected' : '' }}>SSL</option>
                                        <option value="" {{ env('MAIL_ENCRYPTION') == '' ? 'selected' : '' }}>None</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_USERNAME">Mail Username</label>
                                    <input type="text" name="MAIL_USERNAME" class="form-control" value="{{ env('MAIL_USERNAME') }}" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_PASSWORD">Mail Password</label>
                                    <input type="password" name="MAIL_PASSWORD" class="form-control" value="{{ env('MAIL_PASSWORD') }}" placeholder="Your app password">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_FROM_ADDRESS">Mail From Address</label>
                                    <input type="email" name="MAIL_FROM_ADDRESS" class="form-control" value="{{ env('MAIL_FROM_ADDRESS') }}" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="MAIL_FROM_NAME">Mail From Name</label>
                                    <input type="text" name="MAIL_FROM_NAME" class="form-control" value="{{ env('MAIL_FROM_NAME') }}" placeholder="Your Site Name">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Settings</button>
                        </div>
                    </form>

                    <!-- Test Email Section -->
                    <hr>
                    <h5>Test Email Configuration</h5>
                    <form action="{{ route('business_settings.test_email') }}" method="POST">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="test_email">Test Email Address</label>
                                    <input type="email" name="test_email" class="form-control" placeholder="Enter email to test" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-info form-control">Send Test Email</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
