@extends('backend.layouts.app')

@section('title', 'Google Maps Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Google Maps Configuration</h4>
                </div>
                <div class="card-body">
                    <form action="{{ route('business_settings.update') }}" method="POST">
                        @csrf
                        <input type="hidden" name="types[]" value="google_maps_status">
                        <input type="hidden" name="types[]" value="GOOGLE_MAPS_API_KEY">

                        <div class="form-group">
                            <label class="form-label">Enable Google Maps</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" name="google_maps_status" 
                                    {{ get_setting('google_maps_status') == 1 ? 'checked' : '' }}>
                                <label class="form-check-label">Enable Google Maps integration</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="GOOGLE_MAPS_API_KEY">Google Maps API Key</label>
                            <input type="text" name="GOOGLE_MAPS_API_KEY" class="form-control" 
                                value="{{ env('GOOGLE_MAPS_API_KEY') }}" 
                                placeholder="Enter your Google Maps API Key">
                            <small class="form-text text-muted">
                                Required for displaying maps and location services
                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Google Maps Settings</button>
                        </div>
                    </form>

                    <!-- Setup Instructions -->
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Setup Instructions</h5>
                            <ol>
                                <li>Go to <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                                <li>Create a new project or select an existing one</li>
                                <li>Enable the following APIs:
                                    <ul>
                                        <li>Maps JavaScript API</li>
                                        <li>Places API</li>
                                        <li>Geocoding API</li>
                                    </ul>
                                </li>
                                <li>Create credentials (API Key)</li>
                                <li>Restrict the API key to your domain</li>
                                <li>Copy the API key and paste it above</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Current Status</h5>
                            <div class="alert {{ get_setting('google_maps_status') == 1 ? 'alert-success' : 'alert-warning' }}">
                                Google Maps is currently 
                                <strong>{{ get_setting('google_maps_status') == 1 ? 'Enabled' : 'Disabled' }}</strong>
                            </div>
                            
                            @if(env('GOOGLE_MAPS_API_KEY'))
                                <div class="alert alert-success">
                                    <strong>API Key Configured!</strong><br>
                                    Key: {{ substr(env('GOOGLE_MAPS_API_KEY'), 0, 20) }}...
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <strong>No API Key Configured</strong><br>
                                    Please add your Google Maps API Key to enable maps functionality.
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Features -->
                    <hr>
                    <h5>Maps Features</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-map-marker-alt fa-3x text-primary mb-3"></i>
                                    <h6>Location Display</h6>
                                    <p class="text-muted">Show company and job locations on interactive maps</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-search-location fa-3x text-success mb-3"></i>
                                    <h6>Location Search</h6>
                                    <p class="text-muted">Enable location-based job search and filtering</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="fas fa-route fa-3x text-warning mb-3"></i>
                                    <h6>Directions</h6>
                                    <p class="text-muted">Provide directions to job locations</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Map -->
                    @if(env('GOOGLE_MAPS_API_KEY') && get_setting('google_maps_status') == 1)
                    <hr>
                    <h5>Test Map</h5>
                    <div id="test-map" style="height: 300px; border-radius: 8px;"></div>
                    
                    <script>
                        function initMap() {
                            const map = new google.maps.Map(document.getElementById("test-map"), {
                                zoom: 10,
                                center: { lat: 40.7128, lng: -74.0060 }, // New York City
                            });
                            
                            new google.maps.Marker({
                                position: { lat: 40.7128, lng: -74.0060 },
                                map: map,
                                title: "Test Location"
                            });
                        }
                    </script>
                    <script async defer 
                        src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&callback=initMap">
                    </script>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
