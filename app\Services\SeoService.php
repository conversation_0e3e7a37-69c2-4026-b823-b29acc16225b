<?php

namespace App\Services;

use App\Models\JobListing;
use App\Models\Company;
use App\Models\Category;
use App\Models\Page;
use Illuminate\Support\Facades\URL;

class SeoService
{
    /**
     * Generate meta tags for a page
     */
    public function generateMetaTags($title, $description = null, $keywords = null, $image = null, $url = null)
    {
        $siteName = config('app.name', 'JobOctopus');
        $defaultDescription = 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development';
        $defaultKeywords = 'jobs, recruitment, employment, career, courses, professionals, job search';
        $defaultImage = asset('images/logo.png');
        
        $description = $description ?: $defaultDescription;
        $keywords = $keywords ?: $defaultKeywords;
        $image = $image ?: $defaultImage;
        $url = $url ?: URL::current();
        
        return [
            'title' => $title,
            'description' => $description,
            'keywords' => $keywords,
            'image' => $image,
            'url' => $url,
            'site_name' => $siteName,
            'canonical' => $url,
        ];
    }

    /**
     * Generate SEO data for job listing
     */
    public function generateJobSeo(JobListing $job)
    {
        $title = $job->title . ' at ' . $job->company->name . ' | JobOctopus';
        $description = substr(strip_tags($job->description), 0, 160) . '...';
        $keywords = implode(', ', array_merge(
            $job->skills_required ?? [],
            [$job->job_type, $job->location, $job->company->name]
        ));
        
        $image = $job->company->logo ? asset('storage/' . $job->company->logo) : null;
        $url = route('frontend.job.show', $job->slug);
        
        return $this->generateMetaTags($title, $description, $keywords, $image, $url);
    }

    /**
     * Generate SEO data for company
     */
    public function generateCompanySeo(Company $company)
    {
        $title = $company->name . ' - Company Profile | JobOctopus';
        $description = $company->description ? 
            substr(strip_tags($company->description), 0, 160) . '...' :
            'Explore career opportunities at ' . $company->name . '. Find jobs, company information and more.';
        $keywords = implode(', ', array_filter([
            $company->name,
            $company->industry,
            $company->location,
            'jobs',
            'careers',
            'employment'
        ]));
        
        $image = $company->logo ? asset('storage/' . $company->logo) : null;
        $url = route('frontend.company', $company->slug);
        
        return $this->generateMetaTags($title, $description, $keywords, $image, $url);
    }

    /**
     * Generate SEO data for category
     */
    public function generateCategorySeo(Category $category)
    {
        $title = $category->name . ' Jobs | JobOctopus';
        $description = 'Find the latest ' . $category->name . ' jobs and career opportunities. Browse through hundreds of job listings in ' . $category->name . '.';
        $keywords = implode(', ', [
            $category->name . ' jobs',
            $category->name . ' careers',
            $category->name . ' employment',
            'job search',
            'recruitment'
        ]);
        
        $url = route('category.jobs', $category->slug);
        
        return $this->generateMetaTags($title, $description, $keywords, null, $url);
    }

    /**
     * Generate JSON-LD structured data for job listing
     */
    public function generateJobStructuredData(JobListing $job)
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'JobPosting',
            'title' => $job->title,
            'description' => strip_tags($job->description),
            'identifier' => [
                '@type' => 'PropertyValue',
                'name' => $job->company->name,
                'value' => $job->id
            ],
            'datePosted' => $job->created_at->toISOString(),
            'validThrough' => $job->deadline ? $job->deadline->toISOString() : null,
            'employmentType' => strtoupper(str_replace('-', '_', $job->job_type)),
            'hiringOrganization' => [
                '@type' => 'Organization',
                'name' => $job->company->name,
                'sameAs' => $job->company->website,
                'logo' => $job->company->logo ? asset('storage/' . $job->company->logo) : null
            ],
            'jobLocation' => [
                '@type' => 'Place',
                'address' => [
                    '@type' => 'PostalAddress',
                    'addressLocality' => $job->location
                ]
            ],
            'baseSalary' => $job->salary_min ? [
                '@type' => 'MonetaryAmount',
                'currency' => 'USD',
                'value' => [
                    '@type' => 'QuantitativeValue',
                    'minValue' => $job->salary_min,
                    'maxValue' => $job->salary_max,
                    'unitText' => 'YEAR'
                ]
            ] : null,
            'qualifications' => $job->requirements,
            'skills' => implode(', ', $job->skills_required ?? []),
            'workHours' => $job->job_type === 'full-time' ? 'Full-time' : 'Part-time',
            'url' => route('frontend.job.show', $job->slug)
        ];
    }

    /**
     * Generate JSON-LD structured data for organization
     */
    public function generateOrganizationStructuredData(Company $company)
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => $company->name,
            'url' => $company->website,
            'logo' => $company->logo ? asset('storage/' . $company->logo) : null,
            'description' => $company->description,
            'address' => [
                '@type' => 'PostalAddress',
                'addressLocality' => $company->location,
                'streetAddress' => $company->address
            ],
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'telephone' => $company->phone,
                'email' => $company->email,
                'contactType' => 'customer service'
            ],
            'foundingDate' => $company->founded_year,
            'numberOfEmployees' => $company->size,
            'industry' => $company->industry,
            'sameAs' => array_filter([
                $company->website,
                $company->social_media['linkedin'] ?? null,
                $company->social_media['twitter'] ?? null,
                $company->social_media['facebook'] ?? null
            ])
        ];
    }

    /**
     * Generate breadcrumb structured data
     */
    public function generateBreadcrumbStructuredData($breadcrumbs)
    {
        $items = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['name'],
                'item' => $breadcrumb['url']
            ];
        }

        return [
            '@context' => 'https://schema.org',
            '@type' => 'BreadcrumbList',
            'itemListElement' => $items
        ];
    }

    /**
     * Generate website structured data
     */
    public function generateWebsiteStructuredData()
    {
        return [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => config('app.name', 'JobOctopus'),
            'url' => url('/'),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => url('/search_page?keywords={search_term_string}'),
                'query-input' => 'required name=search_term_string'
            ]
        ];
    }

    /**
     * Generate sitemap data
     */
    public function generateSitemapData()
    {
        $urls = [];
        
        // Static pages
        $urls[] = ['url' => url('/'), 'priority' => '1.0', 'changefreq' => 'daily'];
        $urls[] = ['url' => url('/jobs'), 'priority' => '0.9', 'changefreq' => 'daily'];
        $urls[] = ['url' => url('/companies'), 'priority' => '0.8', 'changefreq' => 'weekly'];
        
        // Job listings
        JobListing::where('status', 'published')->chunk(100, function ($jobs) use (&$urls) {
            foreach ($jobs as $job) {
                $urls[] = [
                    'url' => route('frontend.job.show', $job->slug),
                    'priority' => '0.8',
                    'changefreq' => 'weekly',
                    'lastmod' => $job->updated_at->toISOString()
                ];
            }
        });
        
        // Companies
        Company::chunk(100, function ($companies) use (&$urls) {
            foreach ($companies as $company) {
                $urls[] = [
                    'url' => route('frontend.company', $company->slug),
                    'priority' => '0.7',
                    'changefreq' => 'monthly',
                    'lastmod' => $company->updated_at->toISOString()
                ];
            }
        });
        
        // Categories
        Category::whereNull('parent_id')->chunk(100, function ($categories) use (&$urls) {
            foreach ($categories as $category) {
                $urls[] = [
                    'url' => route('category.jobs', $category->slug),
                    'priority' => '0.7',
                    'changefreq' => 'weekly'
                ];
            }
        });
        
        return $urls;
    }
}
