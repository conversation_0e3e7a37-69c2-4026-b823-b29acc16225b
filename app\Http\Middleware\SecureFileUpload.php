<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecureFileUpload
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->hasFile('resume') || $request->hasFile('additional_documents')) {
            $this->validateFileUploads($request);
        }

        return $next($request);
    }

    /**
     * Validate file uploads for security
     */
    protected function validateFileUploads(Request $request): void
    {
        $allowedMimeTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/jpg',
            'image/png'
        ];

        $allowedExtensions = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
        $maxFileSize = 5 * 1024 * 1024; // 5MB

        // Check resume file
        if ($request->hasFile('resume')) {
            $this->validateSingleFile($request->file('resume'), $allowedMimeTypes, $allowedExtensions, $maxFileSize);
        }

        // Check additional documents
        if ($request->hasFile('additional_documents')) {
            foreach ($request->file('additional_documents') as $file) {
                if ($file) {
                    $this->validateSingleFile($file, $allowedMimeTypes, $allowedExtensions, $maxFileSize);
                }
            }
        }
    }

    /**
     * Validate a single file
     */
    protected function validateSingleFile($file, array $allowedMimeTypes, array $allowedExtensions, int $maxFileSize): void
    {
        // Check file size
        if ($file->getSize() > $maxFileSize) {
            abort(413, 'File size exceeds maximum allowed size of 5MB.');
        }

        // Check MIME type
        if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
            abort(415, 'File type not allowed. Only PDF, DOC, DOCX, JPG, JPEG, and PNG files are permitted.');
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            abort(415, 'File extension not allowed.');
        }

        // Check for executable files disguised as documents
        $fileContent = file_get_contents($file->getPathname());
        $suspiciousPatterns = [
            '/\x4D\x5A/', // PE executable header
            '/\x7F\x45\x4C\x46/', // ELF executable header
            '/<script/i', // JavaScript
            '/<\?php/i', // PHP code
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $fileContent)) {
                abort(415, 'File contains suspicious content and cannot be uploaded.');
            }
        }

        // Additional security: Check file signature matches extension
        $this->validateFileSignature($file, $extension);
    }

    /**
     * Validate file signature matches the extension
     */
    protected function validateFileSignature($file, string $extension): void
    {
        $fileSignatures = [
            'pdf' => ['25504446'], // %PDF
            'doc' => ['D0CF11E0A1B11AE1'], // MS Office
            'docx' => ['504B0304'], // ZIP (DOCX is ZIP-based)
            'jpg' => ['FFD8FF'], // JPEG
            'jpeg' => ['FFD8FF'], // JPEG
            'png' => ['89504E47'], // PNG
        ];

        if (!isset($fileSignatures[$extension])) {
            return;
        }

        $fileHandle = fopen($file->getPathname(), 'rb');
        $fileHeader = bin2hex(fread($fileHandle, 8));
        fclose($fileHandle);

        $validSignature = false;
        foreach ($fileSignatures[$extension] as $signature) {
            if (strpos(strtoupper($fileHeader), $signature) === 0) {
                $validSignature = true;
                break;
            }
        }

        if (!$validSignature) {
            abort(415, 'File signature does not match the file extension.');
        }
    }
}
