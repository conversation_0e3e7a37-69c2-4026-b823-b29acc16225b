@extends('frontend.layouts.app')

@section('title', 'Companies - JobOctopus')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="relative py-16 overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0">
            <img src="{{asset('images/company.jpg')}}" alt="Companies Background" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-blue-800/90"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl font-bold mb-4 text-white">Top Companies</h1>
                <p class="text-xl text-blue-100 max-w-2xl mx-auto">
                    Discover leading companies and explore career opportunities with top employers
                </p>
            </div>
        </div>
    </div>

    <!-- Companies Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        @if($companies->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($companies as $company)
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
                        <!-- Company Logo and Name -->
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="flex-shrink-0">
                                @if($company->logo)
                                    <img src="{{url('storage/' . $company->logo)}}"
                                         alt="{{$company->name}}"
                                         class="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white">
                                @else
                                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                        <i class="fa fa-building text-white text-xl"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-semibold text-gray-900 truncate">{{$company->name}}</h3>
                                @if($company->industry)
                                    <p class="text-sm text-gray-600">{{$company->industry}}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Company Details -->
                        <div class="space-y-2 mb-4">
                            @if($company->location)
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fa fa-map-marker-alt w-4 text-gray-400 mr-2"></i>
                                    <span>{{$company->location}}</span>
                                </div>
                            @endif

                            @if($company->size)
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fa fa-users w-4 text-gray-400 mr-2"></i>
                                    <span>{{$company->size}} employees</span>
                                </div>
                            @endif

                            @if($company->founded_year)
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fa fa-calendar w-4 text-gray-400 mr-2"></i>
                                    <span>Founded {{$company->founded_year}}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Job Count -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                                {{$company->job_listings_count}} Open Position{{$company->job_listings_count != 1 ? 's' : ''}}
                            </div>
                            @if($company->website)
                                <a href="{{$company->website}}"
                                   target="_blank"
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fa fa-external-link-alt mr-1"></i>
                                    Website
                                </a>
                            @endif
                        </div>

                        <!-- Description -->
                        @if($company->description)
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                {{Str::limit($company->description, 120)}}
                            </p>
                        @endif

                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <a href="{{route('frontend.company', $company->slug)}}"
                               class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium">
                                View Profile
                            </a>
                            @if($company->job_listings_count > 0)
                                <a href="{{route('frontend.company.jobs', $company->slug)}}"
                                   class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium">
                                    View Jobs
                                </a>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                {{ $companies->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fa fa-building text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Companies Found</h3>
                    <p class="text-gray-600 mb-6">
                        We're working on adding more companies to our platform. Check back soon!
                    </p>
                    <a href="{{route('home')}}"
                       class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fa fa-arrow-left mr-2"></i>
                        Back to Home
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

@push('styles')
<style>
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
</style>
@endpush
@endsection
