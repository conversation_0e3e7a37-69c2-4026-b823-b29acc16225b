@extends('frontend.layouts.app')

@section('title', 'Search Results | JobOctopus')
@section('meta_description', 'Search results for job opportunities. Find your perfect job match.')
@section('meta_keywords', 'job search, employment, career opportunities')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Search Results
                </h1>
                @if(isset($searchParams['keywords']) && $searchParams['keywords'])
                    <p class="text-xl text-white opacity-90 max-w-3xl mx-auto mb-4">
                        Showing results for "<strong>{{ $searchParams['keywords'] }}</strong>"
                    </p>
                @else
                    <p class="text-xl text-white opacity-90 max-w-3xl mx-auto mb-4">
                        Browse all available job opportunities
                    </p>
                @endif
                <div class="flex flex-wrap justify-center gap-4 mt-6">
                    <span class="inline-flex items-center px-4 py-2 rounded-full text-white bg-white bg-opacity-20">
                        <i class="fa fa-search mr-2"></i>
                        {{ $jobs->total() }} Results Found
                    </span>
                    @if($jobs->hasPages())
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-white bg-white bg-opacity-20">
                            <i class="fa fa-file-text-o mr-2"></i>
                            Page {{ $jobs->currentPage() }} of {{ $jobs->lastPage() }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Search Form -->
    <section class="py-8 bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <form action="{{ route('jobs.search') }}" method="GET" class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 shadow-lg border border-gray-200">
                <!-- Basic Search Row -->
                <div class="grid grid-cols-1 md:grid-cols-12 gap-4 mb-6">
                    <div class="md:col-span-5">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fa fa-search mr-1"></i>
                            Keywords, Skills, or Company
                        </label>
                        <input
                            type="text"
                            name="keywords"
                            value="{{ $searchParams['keywords'] ?? '' }}"
                            placeholder="e.g. Software Developer, Marketing, Google"
                            class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900 placeholder-gray-500"
                        >
                    </div>
                    <div class="md:col-span-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fa fa-map-marker mr-1"></i>
                            Location
                        </label>
                        <input
                            type="text"
                            name="location"
                            value="{{ $searchParams['location'] ?? '' }}"
                            placeholder="City, State, or Remote"
                            class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900 placeholder-gray-500"
                        >
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fa fa-sort mr-1"></i>
                            Sort By
                        </label>
                        <select name="sort_by" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900">
                            <option value="latest" {{ ($searchParams['sort_by'] ?? '') == 'latest' ? 'selected' : '' }}>Latest</option>
                            <option value="salary_high" {{ ($searchParams['sort_by'] ?? '') == 'salary_high' ? 'selected' : '' }}>Salary: High to Low</option>
                            <option value="salary_low" {{ ($searchParams['sort_by'] ?? '') == 'salary_low' ? 'selected' : '' }}>Salary: Low to High</option>
                            <option value="oldest" {{ ($searchParams['sort_by'] ?? '') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                        </select>
                    </div>
                    <div class="md:col-span-2 flex items-end">
                        <button type="submit" class="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-lg py-3 px-6 font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                            <i class="fa fa-search mr-2"></i>
                            Search Jobs
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters Toggle -->
                <div class="border-t border-gray-200 pt-6">
                    <button type="button"
                            onclick="toggleAdvancedFilters()"
                            class="flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                        <i class="fa fa-sliders mr-2"></i>
                        <span id="filter-toggle-text">Show Advanced Filters</span>
                        <i class="fa fa-chevron-down ml-2 transform transition-transform duration-200" id="filter-toggle-icon"></i>
                    </button>
                </div>

                <!-- Advanced Filters Section -->
                <div id="advanced-filters" class="hidden mt-6 pt-6 border-t border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Job Category -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fa fa-tags mr-1"></i>
                                Job Category
                            </label>
                            <select name="category" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-2.5 px-3 text-gray-900">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ ($searchParams['category'] ?? '') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Job Type -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fa fa-clock-o mr-1"></i>
                                Job Type
                            </label>
                            <select name="job_type" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-2.5 px-3 text-gray-900">
                                <option value="">All Types</option>
                                @foreach($jobTypes as $type)
                                    <option value="{{ $type }}" {{ ($searchParams['job_type'] ?? '') == $type ? 'selected' : '' }}>
                                        {{ ucfirst(str_replace('-', ' ', $type)) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Experience Level -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fa fa-user mr-1"></i>
                                Experience Level
                            </label>
                            <select name="experience_level" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-2.5 px-3 text-gray-900">
                                <option value="">All Levels</option>
                                @foreach($experienceLevels as $level)
                                    <option value="{{ $level }}" {{ ($searchParams['experience_level'] ?? '') == $level ? 'selected' : '' }}>
                                        {{ $level }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Date Posted -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fa fa-calendar mr-1"></i>
                                Date Posted
                            </label>
                            <select name="date_posted" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-2.5 px-3 text-gray-900">
                                <option value="">Any Time</option>
                                <option value="1" {{ ($searchParams['date_posted'] ?? '') == '1' ? 'selected' : '' }}>Last 24 hours</option>
                                <option value="7" {{ ($searchParams['date_posted'] ?? '') == '7' ? 'selected' : '' }}>Last 7 days</option>
                                <option value="30" {{ ($searchParams['date_posted'] ?? '') == '30' ? 'selected' : '' }}>Last 30 days</option>
                            </select>
                        </div>
                    </div>

                    <!-- Salary Range -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fa fa-money mr-1"></i>
                                Minimum Salary (₹)
                            </label>
                            <input
                                type="number"
                                name="salary_min"
                                value="{{ $searchParams['salary_min'] ?? '' }}"
                                placeholder="e.g. 50000"
                                class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-2.5 px-3 text-gray-900"
                            >
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fa fa-money mr-1"></i>
                                Maximum Salary (₹)
                            </label>
                            <input
                                type="number"
                                name="salary_max"
                                value="{{ $searchParams['salary_max'] ?? '' }}"
                                placeholder="e.g. 150000"
                                class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-2.5 px-3 text-gray-900"
                            >
                        </div>
                    </div>

                    <!-- Remote Work Option -->
                    <div class="mt-6">
                        <label class="flex items-center">
                            <input
                                type="checkbox"
                                name="remote_option"
                                value="1"
                                {{ ($searchParams['remote_option'] ?? '') == '1' ? 'checked' : '' }}
                                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                            <span class="ml-2 text-sm font-medium text-gray-700">
                                <i class="fa fa-home mr-1"></i>
                                Remote work available
                            </span>
                        </label>
                    </div>

                    <!-- Filter Actions -->
                    <div class="flex flex-wrap gap-3 mt-6 pt-6 border-t border-gray-200">
                        <button type="submit" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2.5 rounded-lg hover:shadow-lg transition-all duration-300 font-semibold">
                            <i class="fa fa-filter mr-2"></i>
                            Apply Filters
                        </button>
                        <a href="{{ route('jobs.search') }}" class="bg-gray-200 text-gray-700 px-6 py-2.5 rounded-lg hover:bg-gray-300 transition-all duration-300 font-medium">
                            <i class="fa fa-refresh mr-2"></i>
                            Clear All
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <!-- Jobs Listing -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($jobs->count() > 0)
                <!-- Results Summary -->
                <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8 p-6 bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="mb-4 md:mb-0">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">
                            {{ $jobs->total() }} Jobs Found
                        </h2>
                        <p class="text-gray-600">
                            @if(isset($searchParams['keywords']) && $searchParams['keywords'])
                                Results for "{{ $searchParams['keywords'] }}"
                                @if(isset($searchParams['location']) && $searchParams['location'])
                                    in {{ $searchParams['location'] }}
                                @endif
                            @else
                                All available job opportunities
                            @endif
                        </p>
                    </div>
                    <div class="flex items-center gap-4">
                        <span class="text-sm text-gray-500">
                            Showing {{ $jobs->firstItem() }}-{{ $jobs->lastItem() }} of {{ $jobs->total() }}
                        </span>
                    </div>
                </div>

                <!-- Active Filters Display -->
                @if(array_filter($searchParams))
                    <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div class="flex flex-wrap items-center gap-2">
                            <span class="text-sm font-medium text-blue-800">Active Filters:</span>
                            @foreach($searchParams as $key => $value)
                                @if($value)
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ ucfirst(str_replace('_', ' ', $key)) }}: {{ $value }}
                                        <a href="{{ request()->fullUrlWithQuery([$key => null]) }}" class="ml-1 text-blue-600 hover:text-blue-800">
                                            <i class="fa fa-times"></i>
                                        </a>
                                    </span>
                                @endif
                            @endforeach
                            <a href="{{ route('jobs.search') }}" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
                                Clear all filters
                            </a>
                        </div>
                    </div>
                @endif

                <div class="space-y-6">
                    @foreach($jobs as $job)
                        <div class="transition-all duration-300 hover:-translate-y-0.5 hover:shadow-xl bg-white rounded-xl shadow-sm border border-gray-200 relative overflow-hidden group">
                            <!-- Decorative gradient line -->
                            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-br from-blue-500 via-blue-700 to-red-600"></div>

                            <!-- Featured Job Badge -->
                            @if($job->is_featured)
                                <div class="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                    <i class="fa fa-star mr-1"></i>
                                    Featured
                                </div>
                            @endif

                            <div class="p-6">
                                <div class="flex items-start gap-4 mb-4">
                                    <!-- Company Logo -->
                                    <div class="flex-shrink-0">
                                        @if($job->company && $job->company->logo)
                                            <img src="{{url('storage/' . $job->company->logo)}}" alt="{{$job->company->name}}" class="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white p-2">
                                        @else
                                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                                <i class="fa fa-building text-white text-xl"></i>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Job Details -->
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex-1 min-w-0">
                                                <h3 class="text-xl font-bold text-gray-900 mb-2 hover:text-blue-600 transition-colors group-hover:text-blue-600">
                                                    <a href="#" class="block">{{$job->title}}</a>
                                                </h3>
                                                <p class="text-gray-600 font-semibold text-base mb-1">{{$job->company->name ?? 'Company Name'}}</p>
                                                @if($job->categories->count() > 0)
                                                    <p class="text-sm text-blue-600 font-medium">
                                                        {{ $job->categories->first()->name }}
                                                    </p>
                                                @endif
                                            </div>
                                            <!-- Posted Time & Remote Badge -->
                                            <div class="flex-shrink-0 ml-4 text-right">
                                                <span class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-md block mb-2">{{$job->created_at->diffForHumans()}}</span>
                                                @if($job->remote_option)
                                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                                        <i class="fa fa-home mr-1"></i>
                                                        Remote
                                                    </span>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Job Meta Info -->
                                        <div class="flex flex-wrap gap-2 text-sm text-gray-500 mb-4">
                                            @if($job->experience_level)
                                                <span class="flex items-center bg-gray-100 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-briefcase mr-1.5 text-gray-400"></i>
                                                    {{$job->experience_level}}
                                                </span>
                                            @endif
                                            @if($job->location)
                                                <span class="flex items-center bg-gray-100 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-map-marker mr-1.5 text-gray-400"></i>
                                                    {{$job->location}}
                                                </span>
                                            @endif
                                            @if($job->job_type)
                                                <span class="flex items-center bg-gray-100 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-clock-o mr-1.5 text-gray-400"></i>
                                                    {{ucfirst(str_replace('-', ' ', $job->job_type))}}
                                                </span>
                                            @endif
                                            @if($job->application_deadline)
                                                <span class="flex items-center bg-red-100 text-red-700 px-3 py-1.5 rounded-lg">
                                                    <i class="fa fa-calendar mr-1.5"></i>
                                                    Deadline: {{ $job->application_deadline->format('M d, Y') }}
                                                </span>
                                            @endif
                                        </div>

                                        @if($job->salary_min && $job->salary_max)
                                            <div class="mb-4">
                                                <span class="inline-flex items-center px-4 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-sm">
                                                    <i class="fa fa-money mr-2"></i>
                                                    ₹{{number_format($job->salary_min)}} - ₹{{number_format($job->salary_max)}}
                                                    @if($job->salary_period)
                                                        <span class="ml-1 text-xs opacity-90">/ {{ $job->salary_period }}</span>
                                                    @endif
                                                </span>
                                            </div>
                                        @endif

                                        @if($job->skills_required && is_array($job->skills_required))
                                            <div class="mb-4">
                                                <p class="text-xs font-semibold text-gray-700 mb-2 uppercase tracking-wide">Required Skills:</p>
                                                <div class="flex flex-wrap gap-2">
                                                    @foreach(array_slice($job->skills_required, 0, 5) as $skill)
                                                        <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-3 py-1.5 bg-blue-50 text-blue-700 text-xs rounded-lg border border-blue-200 font-semibold hover:bg-blue-100">{{$skill}}</span>
                                                    @endforeach
                                                    @if(count($job->skills_required) > 5)
                                                        <span class="px-3 py-1.5 bg-gray-100 text-gray-600 text-xs rounded-lg font-semibold">+{{count($job->skills_required) - 5}} more</span>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif

                                        @if($job->description)
                                            <p class="text-gray-600 text-sm leading-relaxed mb-4">
                                                {{Str::limit(strip_tags($job->description), 150)}}
                                            </p>
                                        @endif
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                    <div class="flex items-center gap-3">
                                        <button class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 text-sm font-bold transform hover:scale-105">
                                            <i class="fa fa-paper-plane mr-2"></i>
                                            Apply Now
                                        </button>
                                        <button class="border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-semibold">
                                            <i class="fa fa-bookmark-o mr-1.5"></i>
                                            Save Job
                                        </button>
                                        <button class="border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-semibold">
                                            <i class="fa fa-share mr-1.5"></i>
                                            Share
                                        </button>
                                    </div>
                                    <div class="flex items-center gap-4 text-sm text-gray-400">
                                        <button class="hover:text-red-500 transition-colors p-2 rounded-lg hover:bg-red-50">
                                            <i class="fa fa-heart-o text-base"></i>
                                        </button>
                                        <span class="flex items-center bg-gray-50 px-3 py-2 rounded-lg">
                                            <i class="fa fa-eye mr-1.5"></i>
                                            {{$job->views_count ?? 0}} views
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Enhanced Pagination -->
                <div class="mt-12 flex flex-col items-center">
                    <div class="mb-4">
                        {{ $jobs->appends($searchParams)->links() }}
                    </div>
                    <p class="text-sm text-gray-600">
                        Showing {{ $jobs->firstItem() }} to {{ $jobs->lastItem() }} of {{ $jobs->total() }} results
                    </p>
                </div>
            @else
                <!-- Enhanced No Results Section -->
                <div class="text-center py-20">
                    <div class="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center">
                        <i class="fa fa-search text-gray-400 text-4xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">No Jobs Found</h3>
                    <p class="text-gray-600 mb-8 max-w-md mx-auto">
                        We couldn't find any jobs matching your search criteria. Try adjusting your search terms or filters.
                    </p>

                    <!-- Suggestions -->
                    <div class="bg-blue-50 rounded-xl p-6 max-w-lg mx-auto mb-8">
                        <h4 class="font-semibold text-gray-900 mb-3">Try these suggestions:</h4>
                        <ul class="text-sm text-gray-600 space-y-2 text-left">
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Check your spelling and try different keywords
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Remove some filters to broaden your search
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Try searching for similar job titles
                            </li>
                            <li class="flex items-center">
                                <i class="fa fa-check text-blue-500 mr-2"></i>
                                Expand your location search area
                            </li>
                        </ul>
                    </div>

                    <div class="flex flex-wrap justify-center gap-4">
                        <a href="{{ route('jobs.search') }}" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-300 font-semibold">
                            <i class="fa fa-refresh mr-2"></i>
                            Clear Filters & Browse All
                        </a>
                        <a href="{{ route('home') }}" class="bg-gray-200 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-300 transition-all duration-300 font-semibold">
                            <i class="fa fa-home mr-2"></i>
                            Back to Home
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
</div>

<!-- JavaScript for Advanced Filters -->
<script>
function toggleAdvancedFilters() {
    const filtersDiv = document.getElementById('advanced-filters');
    const toggleText = document.getElementById('filter-toggle-text');
    const toggleIcon = document.getElementById('filter-toggle-icon');

    if (filtersDiv.classList.contains('hidden')) {
        filtersDiv.classList.remove('hidden');
        toggleText.textContent = 'Hide Advanced Filters';
        toggleIcon.classList.add('rotate-180');
    } else {
        filtersDiv.classList.add('hidden');
        toggleText.textContent = 'Show Advanced Filters';
        toggleIcon.classList.remove('rotate-180');
    }
}

// Auto-show advanced filters if any advanced filter is active
document.addEventListener('DOMContentLoaded', function() {
    const searchParams = new URLSearchParams(window.location.search);
    const advancedParams = ['category', 'experience_level', 'salary_min', 'salary_max', 'remote_option', 'date_posted'];

    const hasAdvancedFilters = advancedParams.some(param => searchParams.get(param));

    if (hasAdvancedFilters) {
        toggleAdvancedFilters();
    }
});
</script>

@endsection
