@extends('frontend.layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Job Header -->
    <section class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between">
                <!-- Job Info -->
                <div class="flex-1">
                    <div class="flex items-start space-x-4">
                        @if($job->company->logo)
                            <img src="{{ asset('storage/' . $job->company->logo) }}" 
                                 alt="{{ $job->company->name }}" 
                                 class="w-16 h-16 rounded-lg object-cover">
                        @else
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fa fa-building text-blue-600 text-xl"></i>
                            </div>
                        @endif
                        
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold text-gray-900 mb-2">{{ $job->title }}</h1>
                            <div class="flex flex-wrap items-center gap-4 text-gray-600 mb-4">
                                <span class="flex items-center">
                                    <i class="fa fa-building mr-2"></i>
                                    <a href="{{ route('frontend.company', $job->company->slug) }}" 
                                       class="hover:text-blue-600 font-semibold">{{ $job->company->name }}</a>
                                </span>
                                <span class="flex items-center">
                                    <i class="fa fa-map-marker mr-2"></i>
                                    {{ $job->location }}
                                </span>
                                <span class="flex items-center">
                                    <i class="fa fa-clock-o mr-2"></i>
                                    {{ $job->created_at->diffForHumans() }}
                                </span>
                                @if($job->views_count)
                                    <span class="flex items-center">
                                        <i class="fa fa-eye mr-2"></i>
                                        {{ number_format($job->views_count) }} views
                                    </span>
                                @endif
                            </div>
                            
                            <!-- Job Meta -->
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                    {{ ucfirst(str_replace('_', ' ', $job->job_type)) }}
                                </span>
                                @if($job->experience_level)
                                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                        {{ $job->experience_level }}
                                    </span>
                                @endif
                                @if($job->remote_option)
                                    <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">
                                        Remote
                                    </span>
                                @endif
                                @foreach($job->categories as $category)
                                    <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                                        {{ $category->name }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Apply Button -->
                <div class="mt-6 lg:mt-0 lg:ml-8">
                    @auth
                        @if(auth()->user()->isCandidate())
                            @if($hasApplied)
                                <button disabled class="w-full lg:w-auto px-8 py-3 bg-gray-400 text-white rounded-lg font-semibold cursor-not-allowed">
                                    <i class="fa fa-check mr-2"></i>
                                    Already Applied
                                </button>
                            @else
                                <a href="{{ route('applications.create', ['job_id' => $job->id]) }}" 
                                   class="inline-block w-full lg:w-auto px-8 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center">
                                    <i class="fa fa-paper-plane mr-2"></i>
                                    Apply Now
                                </a>
                            @endif
                        @else
                            <p class="text-gray-600 text-sm">Only candidates can apply for jobs</p>
                        @endif
                    @else
                        <a href="{{ route('login') }}" 
                           class="inline-block w-full lg:w-auto px-8 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center">
                            <i class="fa fa-sign-in mr-2"></i>
                            Login to Apply
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </section>

    <!-- Job Content -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Job Description -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Job Description</h2>
                        <div class="prose max-w-none text-gray-700">
                            {!! nl2br(e($job->description)) !!}
                        </div>
                    </div>

                    <!-- Requirements -->
                    @if($job->requirements)
                        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                            <div class="prose max-w-none text-gray-700">
                                {!! nl2br(e($job->requirements)) !!}
                            </div>
                        </div>
                    @endif

                    <!-- Skills Required -->
                    @if($job->skills_required && count($job->skills_required) > 0)
                        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Skills Required</h2>
                            <div class="flex flex-wrap gap-2">
                                @foreach($job->skills_required as $skill)
                                    <span class="px-3 py-2 bg-blue-50 text-blue-700 rounded-lg text-sm font-medium">
                                        {{ $skill }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Benefits -->
                    @if($job->benefits)
                        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Benefits</h2>
                            <div class="prose max-w-none text-gray-700">
                                {!! nl2br(e($job->benefits)) !!}
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Job Summary -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Job Summary</h3>
                        <div class="space-y-3">
                            @if($job->salary_min || $job->salary_max)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Salary:</span>
                                    <span class="font-semibold">
                                        @if($job->salary_min && $job->salary_max)
                                            ${{ number_format($job->salary_min) }} - ${{ number_format($job->salary_max) }}
                                        @elseif($job->salary_min)
                                            From ${{ number_format($job->salary_min) }}
                                        @else
                                            Up to ${{ number_format($job->salary_max) }}
                                        @endif
                                        @if($job->salary_period)
                                            / {{ $job->salary_period }}
                                        @endif
                                    </span>
                                </div>
                            @endif
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Job Type:</span>
                                <span class="font-semibold">{{ ucfirst(str_replace('_', ' ', $job->job_type)) }}</span>
                            </div>
                            
                            @if($job->experience_level)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Experience:</span>
                                    <span class="font-semibold">{{ $job->experience_level }}</span>
                                </div>
                            @endif
                            
                            @if($job->education_level)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Education:</span>
                                    <span class="font-semibold">{{ $job->education_level }}</span>
                                </div>
                            @endif
                            
                            @if($job->application_deadline)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Deadline:</span>
                                    <span class="font-semibold">{{ $job->application_deadline->format('M d, Y') }}</span>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Company Info -->
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">About {{ $job->company->name }}</h3>
                        @if($job->company->description)
                            <p class="text-gray-700 mb-4">{{ Str::limit($job->company->description, 150) }}</p>
                        @endif
                        <a href="{{ route('frontend.company', $job->company->slug) }}" 
                           class="text-blue-600 hover:text-blue-700 font-semibold">
                            View Company Profile →
                        </a>
                    </div>

                    <!-- Share Job -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-4">Share this Job</h3>
                        <div class="flex space-x-3">
                            <a href="{{ get_social_share_url('facebook', request()->url(), $job->title) }}" 
                               target="_blank" 
                               class="flex-1 bg-blue-600 text-white text-center py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fa fa-facebook"></i>
                            </a>
                            <a href="{{ get_social_share_url('twitter', request()->url(), $job->title) }}" 
                               target="_blank" 
                               class="flex-1 bg-blue-400 text-white text-center py-2 rounded-lg hover:bg-blue-500 transition-colors">
                                <i class="fa fa-twitter"></i>
                            </a>
                            <a href="{{ get_social_share_url('linkedin', request()->url(), $job->title) }}" 
                               target="_blank" 
                               class="flex-1 bg-blue-700 text-white text-center py-2 rounded-lg hover:bg-blue-800 transition-colors">
                                <i class="fa fa-linkedin"></i>
                            </a>
                            <a href="{{ get_social_share_url('email', request()->url(), $job->title, 'Check out this job opportunity') }}" 
                               class="flex-1 bg-gray-600 text-white text-center py-2 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fa fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Similar Jobs -->
    @if($similarJobs->count() > 0)
        <section class="py-8 bg-gray-100">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Similar Jobs</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($similarJobs as $similarJob)
                        <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                <a href="{{ route('frontend.job.show', $similarJob->slug) }}" 
                                   class="hover:text-blue-600">{{ $similarJob->title }}</a>
                            </h3>
                            <p class="text-gray-600 mb-2">{{ $similarJob->company->name }}</p>
                            <p class="text-sm text-gray-500 mb-3">{{ $similarJob->location }}</p>
                            <div class="flex justify-between items-center">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                    {{ ucfirst(str_replace('_', ' ', $similarJob->job_type)) }}
                                </span>
                                <span class="text-xs text-gray-400">{{ $similarJob->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif
</div>
@endsection
