<x-guest-layout>
    <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Create your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
            Or
            <a href="{{ route('login') }}" class="font-medium text-indigo-600 hover:text-indigo-500">
                sign in to your existing account
            </a>
        </p>
    </div>

    <form class="mt-8 space-y-6" method="POST" action="{{ route('register') }}">
        @csrf

        <div class="space-y-4">
            <!-- Name -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Full name</label>
                <input id="name" name="name" type="text" autocomplete="name" required
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                       placeholder="Full name" value="{{ old('name') }}">
                <x-input-error :messages="$errors->get('name')" class="mt-1" />
            </div>

            <!-- Email -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                <input id="email" name="email" type="email" autocomplete="email" required
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                       placeholder="Email address" value="{{ old('email') }}">
                <x-input-error :messages="$errors->get('email')" class="mt-1" />
            </div>

            <!-- Password -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                <input id="password" name="password" type="password" autocomplete="new-password" required
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                       placeholder="Password">
                <x-input-error :messages="$errors->get('password')" class="mt-1" />
            </div>

            <!-- Confirm Password -->
            <div>
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm password</label>
                <input id="password_confirmation" name="password_confirmation" type="password" autocomplete="new-password" required
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                       placeholder="Confirm password">
                <x-input-error :messages="$errors->get('password_confirmation')" class="mt-1" />
            </div>

            <!-- Role Selection -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Account type</label>
                <div class="space-y-2">
                    <label class="relative flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 {{ old('role') == 'candidate' || old('role') == '' ? 'border-indigo-500 bg-indigo-50' : '' }}">
                        <input type="radio" name="role" value="candidate" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300" {{ old('role') == 'candidate' || old('role') == '' ? 'checked' : '' }} required>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900">Job Seeker</div>
                            <div class="text-sm text-gray-500">Looking for job opportunities</div>
                        </div>
                    </label>

                    <label class="relative flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50 {{ old('role') == 'employer' ? 'border-indigo-500 bg-indigo-50' : '' }}">
                        <input type="radio" name="role" value="employer" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300" {{ old('role') == 'employer' ? 'checked' : '' }} required>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900">Employer</div>
                            <div class="text-sm text-gray-500">Hiring candidates and posting jobs</div>
                        </div>
                    </label>
                </div>
                <x-input-error :messages="$errors->get('role')" class="mt-1" />
            </div>
        </div>

        <div>
            <button type="submit"
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                    <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                    </svg>
                </span>
                Create account
            </button>
        </div>
    </form>
</x-guest-layout>
