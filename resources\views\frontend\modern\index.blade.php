@extends('frontend.layouts.app')

@section('title', 'Jobs-Recruitment-Employment-Career-Courses-Professionals | JobOctopus')
@section('meta_description', 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development')
@section('meta_keywords', 'jobs, recruitment, employment, career, courses, professionals, job search')

@section('content')

    <!-- Hero/Banner Section with Enhanced Gradient and Background -->
    <section class="relative overflow-hidden" style="padding: 80px 0 120px 0;">
        <!-- Background Image -->
        <div class="absolute inset-0">
            <img src="{{asset('images/header-bg2.jpg')}}" alt="Background" class="w-full h-full object-cover">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/90 via-blue-700/85 to-red-600/90"></div>
        </div>

        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px); background-size: 50px 50px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white opacity-10 rounded-full" style="filter: blur(20px);"></div>
        <div class="absolute bottom-20 right-10 w-32 h-32 bg-white opacity-10 rounded-full" style="filter: blur(30px);"></div>
        <div class="absolute top-1/2 right-1/4 w-16 h-16 bg-white opacity-10 rounded-full" style="filter: blur(15px);"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center" style="margin-bottom: 60px;">
                <h1 class="text-white font-bold leading-tight" style="font-size: clamp(2.5rem, 5vw, 4rem); margin-bottom: 24px;">
                    Find Your <span class="text-yellow-300">Dream Job</span>
                </h1>
                <p class="text-white max-w-3xl mx-auto leading-relaxed" style="font-size: clamp(1.125rem, 2vw, 1.5rem); opacity: 0.9; margin-bottom: 40px;">
                    Discover thousands of opportunities from top companies and kickstart your career journey today
                </p>
            </div>

            <div class="flex items-center justify-center">
                <div class="w-full max-w-6xl">
                    <!-- Enhanced Search Form -->
                    <form action="{{url('search_page')}}" method="GET"
                          x-data="{
                              activeTab: 'employment',
                              showAdvanced: false,
                              salaryMin: '',
                              salaryMax: '',
                              dateFrom: '',
                              dateTo: '',
                              selectedEmploymentTypes: [],
                              selectedWorkLocations: []
                          }"
                          class="space-y-6">

                        <!-- Main Search Bar -->
                        <div class="bg-white rounded-2xl shadow-2xl p-6" style="background: rgba(255, 255, 255, 0.98);">
                            <div class="grid grid-cols-1 md:grid-cols-12 gap-4">
                                <!-- Keywords Input -->
                                <div class="md:col-span-5">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fa fa-search text-gray-400 text-lg"></i>
                                        </div>
                                        <input
                                            type="text"
                                            name="keywords"
                                            placeholder="Enter Keywords, Skills, Designation..."
                                            class="w-full rounded-xl border-0 text-gray-700 shadow-lg text-lg placeholder-gray-500 bg-white focus:ring-4 focus:ring-blue-200 transition-all duration-300"
                                            style="padding: 18px 24px 18px 48px;"
                                        >
                                    </div>
                                </div>

                                <!-- Location Input -->
                                <div class="md:col-span-4">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fa fa-map-marker text-gray-400 text-lg"></i>
                                        </div>
                                        <input
                                            type="text"
                                            name="location"
                                            placeholder="Enter Location"
                                            class="w-full rounded-xl border-0 text-gray-700 shadow-lg text-lg placeholder-gray-500 bg-white focus:ring-4 focus:ring-blue-200 transition-all duration-300"
                                            style="padding: 18px 24px 18px 48px;"
                                        >
                                    </div>
                                </div>

                                <!-- Search Button -->
                                <div class="md:col-span-3">
                                    <button
                                        type="submit"
                                        class="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white rounded-xl font-bold flex items-center justify-center shadow-xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl"
                                        style="padding: 18px 24px;"
                                    >
                                        <i class="fa fa-search mr-2"></i>
                                        Search
                                    </button>
                                </div>
                            </div>

                            <!-- Search Options Toggle -->
                            <div class="mt-6 text-center">
                                <button
                                    type="button"
                                    @click="showAdvanced = !showAdvanced"
                                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-blue-500"
                                >
                                    <i class="fa fa-sliders mr-2"></i>
                                    <span x-text="showAdvanced ? 'Hide Advanced Filters' : 'Show Advanced Filters'"></span>
                                    <i class="fa fa-chevron-down ml-2 transition-transform duration-300" :class="showAdvanced ? 'rotate-180' : ''"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Advanced Search Filters -->
                        <div x-show="showAdvanced"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 -translate-y-4"
                             x-transition:enter-end="opacity-100 translate-y-0"
                             x-transition:leave="transition ease-in duration-200"
                             x-transition:leave-start="opacity-100 translate-y-0"
                             x-transition:leave-end="opacity-0 -translate-y-4"
                             class="bg-white rounded-2xl shadow-2xl overflow-hidden"
                             style="background: rgba(255, 255, 255, 0.98);">

                            <!-- Filter Tabs -->
                            <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" @click="activeTab = 'employment'"
                                            :class="activeTab === 'employment' ? 'bg-red-600 text-white' : 'bg-white/20 text-white hover:bg-white/30'"
                                            class="px-4 py-2 rounded-lg font-medium transition-all duration-300">
                                        Employment Types
                                    </button>
                                    <button type="button" @click="activeTab = 'salary'"
                                            :class="activeTab === 'salary' ? 'bg-red-600 text-white' : 'bg-white/20 text-white hover:bg-white/30'"
                                            class="px-4 py-2 rounded-lg font-medium transition-all duration-300">
                                        Salary - Remuneration
                                    </button>
                                    <button type="button" @click="activeTab = 'time'"
                                            :class="activeTab === 'time' ? 'bg-red-600 text-white' : 'bg-white/20 text-white hover:bg-white/30'"
                                            class="px-4 py-2 rounded-lg font-medium transition-all duration-300">
                                        Sort by Time
                                    </button>
                                    <button type="button" @click="activeTab = 'location'"
                                            :class="activeTab === 'location' ? 'bg-red-600 text-white' : 'bg-white/20 text-white hover:bg-white/30'"
                                            class="px-4 py-2 rounded-lg font-medium transition-all duration-300">
                                        Work Location Type
                                    </button>
                                </div>
                            </div>

                            <!-- Filter Content -->
                            <div class="p-6">
                                <!-- Employment Types Tab -->
                                <div x-show="activeTab === 'employment'"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0"
                                     x-transition:enter-end="opacity-100">
                                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="all" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">All</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="full-time" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Full-time</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="part-time" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Part-time</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="casual" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Casual</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="contract" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Contract</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="trainee" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Trainee</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="internship" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Internship</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="online" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Online</span>
                                        </label>
                                        <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="employment_types[]" value="commission" class="sr-only">
                                            <span class="text-sm font-medium text-gray-700">Commission</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Salary Tab -->
                                <div x-show="activeTab === 'salary'"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0"
                                     x-transition:enter-end="opacity-100">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Salary</label>
                                            <select name="salary_min" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900">
                                                <option value="">Select Minimum</option>
                                                <option value="20000">₹20,000</option>
                                                <option value="30000">₹30,000</option>
                                                <option value="50000">₹50,000</option>
                                                <option value="75000">₹75,000</option>
                                                <option value="100000">₹1,00,000</option>
                                                <option value="150000">₹1,50,000</option>
                                                <option value="200000">₹2,00,000</option>
                                                <option value="300000">₹3,00,000</option>
                                                <option value="500000">₹5,00,000</option>
                                                <option value="1000000">₹10,00,000+</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Maximum Salary</label>
                                            <select name="salary_max" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900">
                                                <option value="">Select Maximum</option>
                                                <option value="30000">₹30,000</option>
                                                <option value="50000">₹50,000</option>
                                                <option value="75000">₹75,000</option>
                                                <option value="100000">₹1,00,000</option>
                                                <option value="150000">₹1,50,000</option>
                                                <option value="200000">₹2,00,000</option>
                                                <option value="300000">₹3,00,000</option>
                                                <option value="500000">₹5,00,000</option>
                                                <option value="1000000">₹10,00,000</option>
                                                <option value="2000000">₹20,00,000+</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Time Tab -->
                                <div x-show="activeTab === 'time'"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0"
                                     x-transition:enter-end="opacity-100">
                                    <div class="space-y-6">
                                        <!-- Quick Time Filters -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-3">Quick Filters</label>
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                                <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                                    <input type="radio" name="date_posted" value="1" class="sr-only">
                                                    <span class="text-sm font-medium text-gray-700">Last 24 hours</span>
                                                </label>
                                                <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                                    <input type="radio" name="date_posted" value="7" class="sr-only">
                                                    <span class="text-sm font-medium text-gray-700">Last 7 days</span>
                                                </label>
                                                <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                                    <input type="radio" name="date_posted" value="30" class="sr-only">
                                                    <span class="text-sm font-medium text-gray-700">Last 30 days</span>
                                                </label>
                                                <label class="flex items-center justify-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                                    <input type="radio" name="date_posted" value="90" class="sr-only">
                                                    <span class="text-sm font-medium text-gray-700">Last 3 months</span>
                                                </label>
                                            </div>
                                        </div>

                                        <!-- Custom Date Range -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-3">Custom Date Range</label>
                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                                                <div>
                                                    <label class="block text-xs text-gray-500 mb-1">From</label>
                                                    <input type="date" name="date_from" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900">
                                                </div>
                                                <div>
                                                    <label class="block text-xs text-gray-500 mb-1">To</label>
                                                    <input type="date" name="date_to" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 py-3 px-4 text-gray-900">
                                                </div>
                                                <div>
                                                    <button type="button" class="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg py-3 px-4 font-medium transition-colors duration-300">
                                                        Apply Range
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Work Location Type Tab -->
                                <div x-show="activeTab === 'location'"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0"
                                     x-transition:enter-end="opacity-100">
                                    <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                                        <label class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="work_location[]" value="all" class="sr-only">
                                            <div class="text-center">
                                                <i class="fa fa-globe text-blue-600 text-xl mb-2 block"></i>
                                                <span class="text-sm font-medium text-gray-700">All</span>
                                            </div>
                                        </label>
                                        <label class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="work_location[]" value="onsite" class="sr-only">
                                            <div class="text-center">
                                                <i class="fa fa-building text-green-600 text-xl mb-2 block"></i>
                                                <span class="text-sm font-medium text-gray-700">OnSite</span>
                                            </div>
                                        </label>
                                        <label class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="work_location[]" value="onshore" class="sr-only">
                                            <div class="text-center">
                                                <i class="fa fa-map-marker text-purple-600 text-xl mb-2 block"></i>
                                                <span class="text-sm font-medium text-gray-700">OnShore</span>
                                            </div>
                                        </label>
                                        <label class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="work_location[]" value="offshore" class="sr-only">
                                            <div class="text-center">
                                                <i class="fa fa-ship text-orange-600 text-xl mb-2 block"></i>
                                                <span class="text-sm font-medium text-gray-700">OffShore</span>
                                            </div>
                                        </label>
                                        <label class="flex items-center justify-center p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 has-[:checked]:border-blue-500 has-[:checked]:bg-blue-50">
                                            <input type="checkbox" name="work_location[]" value="remote" class="sr-only">
                                            <div class="text-center">
                                                <i class="fa fa-home text-red-600 text-xl mb-2 block"></i>
                                                <span class="text-sm font-medium text-gray-700">Work From Home</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Popular Searches -->
                        <div class="text-center">
                            <p class="text-white font-medium mb-4" style="opacity: 0.9;">Popular Searches:</p>
                            <div class="flex flex-wrap justify-center gap-3">
                                <a href="{{ route('jobs.search', ['job_type' => 'volunteering']) }}" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white hover:bg-white hover:text-blue-600 transform hover:scale-105" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.3);">
                                    <i class="fa fa-heart"></i>
                                    <span>Volunteering</span>
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'casual']) }}" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white hover:bg-white hover:text-blue-600 transform hover:scale-105" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.3);">
                                    <i class="fa fa-clock-o"></i>
                                    <span>Casual</span>
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'internship']) }}" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white hover:bg-white hover:text-blue-600 transform hover:scale-105" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.3);">
                                    <i class="fa fa-graduation-cap"></i>
                                    <span>Internship</span>
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'business']) }}" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white hover:bg-white hover:text-blue-600 transform hover:scale-105" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.3);">
                                    <i class="fa fa-building"></i>
                                    <span>Business</span>
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'franchise']) }}" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white hover:bg-white hover:text-blue-600 transform hover:scale-105" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.3);">
                                    <i class="fa fa-handshake-o"></i>
                                    <span>Franchise</span>
                                </a>
                                <a href="{{ route('jobs.search', ['job_type' => 'part-time']) }}" class="flex items-center space-x-2 text-white text-sm transition-all duration-300 rounded-full border border-white hover:bg-white hover:text-blue-600 transform hover:scale-105" style="background: rgba(255, 255, 255, 0.15); padding: 10px 16px; border-color: rgba(255, 255, 255, 0.3);">
                                    <i class="fa fa-calendar"></i>
                                    <span>Part-time</span>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <!-- Featured Job Categories - Full Width -->
        @if(isset($featuredCategories) && $featuredCategories->count() > 0)
            <section class="mb-12">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Job Categories</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">Explore opportunities across various industries and find your perfect career match</p>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    @foreach($featuredCategories as $category)
                        @php
                            $categoryIcons = [
                                'technology' => 'fa-laptop-code',
                                'healthcare' => 'fa-stethoscope',
                                'finance' => 'fa-chart-line',
                                'education' => 'fa-graduation-cap',
                                'marketing' => 'fa-bullhorn',
                                'sales' => 'fa-handshake',
                                'engineering' => 'fa-cogs',
                                'design' => 'fa-paint-brush',
                                'accounting' => 'fa-calculator',
                                'legal' => 'fa-balance-scale',
                                'hr' => 'fa-users',
                                'media' => 'fa-camera',
                                'construction' => 'fa-hard-hat',
                                'retail' => 'fa-shopping-cart',
                                'hospitality' => 'fa-concierge-bell',
                                'transportation' => 'fa-truck',
                                'manufacturing' => 'fa-industry',
                                'consulting' => 'fa-lightbulb',
                                'real-estate' => 'fa-home',
                                'government' => 'fa-university'
                            ];
                            $categoryColors = [
                                'technology' => 'from-blue-500 to-blue-700',
                                'healthcare' => 'from-green-500 to-green-700',
                                'finance' => 'from-emerald-500 to-emerald-700',
                                'education' => 'from-purple-500 to-purple-700',
                                'marketing' => 'from-pink-500 to-pink-700',
                                'sales' => 'from-orange-500 to-orange-700',
                                'engineering' => 'from-gray-500 to-gray-700',
                                'design' => 'from-indigo-500 to-indigo-700',
                                'accounting' => 'from-yellow-500 to-yellow-700',
                                'legal' => 'from-red-500 to-red-700',
                                'hr' => 'from-teal-500 to-teal-700',
                                'media' => 'from-cyan-500 to-cyan-700',
                                'construction' => 'from-amber-500 to-amber-700',
                                'retail' => 'from-lime-500 to-lime-700',
                                'hospitality' => 'from-rose-500 to-rose-700',
                                'transportation' => 'from-slate-500 to-slate-700',
                                'manufacturing' => 'from-stone-500 to-stone-700',
                                'consulting' => 'from-violet-500 to-violet-700',
                                'real-estate' => 'from-sky-500 to-sky-700',
                                'government' => 'from-neutral-500 to-neutral-700'
                            ];
                            $slug = strtolower(str_replace([' ', '&', '-'], ['', '', ''], $category->slug));
                            $icon = $categoryIcons[$slug] ?? 'fa-briefcase';
                            $color = $categoryColors[$slug] ?? 'from-blue-500 to-blue-700';
                        @endphp
                        <a href="{{url('category/' . $category->slug)}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br {{$color}} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                                <i class="fa {{$icon}} text-white text-2xl"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 text-sm mb-1">{{$category->name}}</h3>
                            <p class="text-xs text-blue-600 font-medium">{{$category->job_listings_count ?? 0}} Jobs</p>
                        </a>
                    @endforeach
                </div>
            </section>
        @else
            <section class="mb-12">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Job Categories</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">Explore opportunities across various industries and find your perfect career match</p>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <a href="{{url('accounting')}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-500 to-yellow-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-calculator text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Accounting</h3>
                        <p class="text-xs text-blue-600 font-medium">25 Jobs</p>
                    </a>
                    <a href="{{url('development')}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-blue-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-code text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Development</h3>
                        <p class="text-xs text-blue-600 font-medium">45 Jobs</p>
                    </a>
                    <a href="{{url('technology')}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-500 to-indigo-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-laptop-code text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Technology</h3>
                        <p class="text-xs text-blue-600 font-medium">38 Jobs</p>
                    </a>
                    <a href="{{url('media_news')}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyan-500 to-cyan-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-newspaper text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Media & News</h3>
                        <p class="text-xs text-blue-600 font-medium">12 Jobs</p>
                    </a>
                    <a href="{{url('medical')}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-500 to-green-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-stethoscope text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Medical</h3>
                        <p class="text-xs text-blue-600 font-medium">22 Jobs</p>
                    </a>
                    <a href="{{url('govt')}}" class="transition-all duration-300 hover:-translate-y-1 hover:shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center shadow-sm hover:shadow-xl border border-blue-100 group">
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-500 to-purple-700 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                            <i class="fa fa-university text-white text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 text-sm mb-1">Government</h3>
                        <p class="text-xs text-blue-600 font-medium">18 Jobs</p>
                    </a>
                </div>
            </section>
        @endif



        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Main Content Area -->
            <div class="lg:col-span-3">

                <!-- Recent Jobs -->
                <section>
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-2">Recent Jobs</h2>
                            <p class="text-gray-600">Latest opportunities from top companies</p>
                        </div>
                        <a href="{{url('jobs')}}" class="text-blue-600 hover:text-blue-700 font-medium flex items-center transition-colors">
                            View All Jobs
                            <i class="fa fa-arrow-right ml-2"></i>
                        </a>
                    </div>

                    <div class="space-y-4">
                        @if(isset($recentJobs) && $recentJobs->count() > 0)
                            @foreach($recentJobs as $job)
                                <div class="transition-all duration-300 hover:-translate-y-0.5 hover:shadow-xl bg-white rounded-xl shadow-sm border border-gray-200 relative overflow-hidden">
                                    <!-- Decorative gradient line -->
                                    <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-br from-blue-500 via-blue-700 to-red-600"></div>

                                    <div class="p-6">
                                        <div class="flex items-start gap-4 mb-4">
                                            <!-- Company Logo -->
                                            <div class="flex-shrink-0">
                                                @if($job->company && $job->company->logo)
                                                    <img src="{{url('storage/' . $job->company->logo)}}" alt="{{$job->company->name}}" class="w-14 h-14 object-contain rounded-lg border border-gray-200 bg-white">
                                                @else
                                                    @php
                                                        $jobCompanyColors = [
                                                            'from-blue-500 to-blue-600',
                                                            'from-green-500 to-green-600',
                                                            'from-purple-500 to-purple-600',
                                                            'from-red-500 to-red-600',
                                                            'from-yellow-500 to-yellow-600',
                                                            'from-indigo-500 to-indigo-600',
                                                            'from-pink-500 to-pink-600',
                                                            'from-teal-500 to-teal-600'
                                                        ];
                                                        $companyName = $job->company ? $job->company->name : $job->title;
                                                        $colorIndex = crc32($companyName) % count($jobCompanyColors);
                                                        $color = $jobCompanyColors[$colorIndex];
                                                    @endphp
                                                    <div class="w-14 h-14 bg-gradient-to-br {{$color}} rounded-lg flex items-center justify-center shadow-sm">
                                                        @if($job->company)
                                                            <span class="text-white font-bold text-lg">{{strtoupper(substr($job->company->name, 0, 1))}}</span>
                                                        @else
                                                            <i class="fa fa-briefcase text-white text-lg"></i>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Job Details -->
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-start justify-between mb-2">
                                                    <div class="flex-1 min-w-0">
                                                        <h3 class="text-xl font-semibold text-gray-900 mb-1 hover:text-blue-600 transition-colors">
                                                            <a href="{{ route('frontend.job.show', $job->slug) }}" class="block truncate">{{$job->title}}</a>
                                                        </h3>
                                                        <p class="text-gray-600 font-medium text-base">{{$job->company->name ?? 'Company Name'}}</p>
                                                    </div>
                                                    <!-- Posted Time -->
                                                    <div class="flex-shrink-0 ml-4">
                                                        <span class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-md">{{$job->created_at->diffForHumans()}}</span>
                                                    </div>
                                                </div>

                                                <!-- Job Meta Info -->
                                                <div class="flex flex-wrap gap-2 text-sm text-gray-500 mb-3">
                                                    @if($job->experience_level)
                                                        <span class="flex items-center bg-slate-50 border border-slate-200 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 px-2.5 py-1.5 rounded-md">
                                                            <i class="fa fa-briefcase mr-1.5 text-gray-400"></i>
                                                            {{$job->experience_level}}
                                                        </span>
                                                    @endif
                                                    @if($job->location)
                                                        <span class="flex items-center bg-slate-50 border border-slate-200 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 px-2.5 py-1.5 rounded-md">
                                                            <i class="fa fa-map-marker mr-1.5 text-gray-400"></i>
                                                            {{$job->location}}
                                                        </span>
                                                    @endif
                                                    @if($job->job_type)
                                                        <span class="flex items-center bg-slate-50 border border-slate-200 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 px-2.5 py-1.5 rounded-md">
                                                            <i class="fa fa-clock-o mr-1.5 text-gray-400"></i>
                                                            {{ucfirst($job->job_type)}}
                                                        </span>
                                                    @endif
                                                </div>

                                                @if($job->salary_min && $job->salary_max)
                                                    <div class="mb-3">
                                                        <span class="inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-semibold bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-sm">
                                                            <i class="fa fa-money mr-2"></i>
                                                            ₹{{number_format($job->salary_min)}} - ₹{{number_format($job->salary_max)}}
                                                        </span>
                                                    </div>
                                                @endif

                                                @if($job->skills_required && is_array($job->skills_required))
                                                    <div class="mb-4">
                                                        <div class="flex flex-wrap gap-2">
                                                            @foreach(array_slice($job->skills_required, 0, 4) as $skill)
                                                                <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">{{$skill}}</span>
                                                            @endforeach
                                                            @if(count($job->skills_required) > 4)
                                                                <span class="px-2.5 py-1 bg-gray-100 text-gray-600 text-xs rounded-md font-medium">+{{count($job->skills_required) - 4}} more</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endif

                                                @if($job->description)
                                                    <p class="text-gray-600 text-sm leading-relaxed line-clamp-2">
                                                        {{Str::limit(strip_tags($job->description), 120)}}
                                                    </p>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                            <div class="flex items-center gap-3">
                                                <a href="{{ route('frontend.job.show', $job->slug) }}" class="bg-gradient-to-br from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2.5 rounded-lg hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 text-sm font-semibold inline-block">
                                                    <i class="fa fa-paper-plane mr-2"></i>
                                                    Apply Now
                                                </a>
                                                <button class="border border-gray-300 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-medium">
                                                    <i class="fa fa-bookmark-o mr-1.5"></i>
                                                    Save
                                                </button>
                                            </div>
                                            <div class="flex items-center gap-4 text-sm text-gray-400">
                                                <button class="hover:text-red-500 transition-colors p-1">
                                                    <i class="fa fa-heart-o text-base"></i>
                                                </button>
                                                <span class="flex items-center bg-gray-50 px-2 py-1 rounded-md">
                                                    <i class="fa fa-eye mr-1.5"></i>
                                                    {{$job->views_count ?? 0}}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <!-- Sample Job Cards for Demo -->
                            <div class="transition-all duration-300 hover:-translate-y-0.5 hover:shadow-xl bg-white rounded-xl shadow-sm border border-gray-200 relative overflow-hidden">
                                <!-- Decorative gradient line -->
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-br from-blue-500 via-blue-700 to-red-600"></div>

                                <div class="p-6">
                                    <div class="flex items-start gap-4 mb-4">
                                        <!-- Company Logo -->
                                        <div class="flex-shrink-0">
                                            <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-sm">
                                                <span class="text-white font-bold text-lg">Z</span>
                                            </div>
                                        </div>

                                        <!-- Job Details -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-start justify-between mb-2">
                                                <div class="flex-1 min-w-0">
                                                    <h3 class="text-xl font-semibold text-gray-900 mb-1 hover:text-blue-600 transition-colors">
                                                        <a href="#" class="block truncate">Java Developer</a>
                                                    </h3>
                                                    <p class="text-gray-600 font-medium text-base">ZHANOX INFOTECH PVT LTD.</p>
                                                </div>
                                                <!-- Posted Time -->
                                                <div class="flex-shrink-0 ml-4">
                                                    <span class="text-xs text-gray-400 bg-gray-50 px-2 py-1 rounded-md">2 days ago</span>
                                                </div>
                                            </div>

                                            <!-- Job Meta Info -->
                                            <div class="flex flex-wrap gap-2 text-sm text-gray-500 mb-3">
                                                <span class="flex items-center bg-slate-50 border border-slate-200 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 px-2.5 py-1.5 rounded-md">
                                                    <i class="fa fa-briefcase mr-1.5 text-gray-400"></i>
                                                    0-4 years
                                                </span>
                                                <span class="flex items-center bg-slate-50 border border-slate-200 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 px-2.5 py-1.5 rounded-md">
                                                    <i class="fa fa-map-marker mr-1.5 text-gray-400"></i>
                                                    Bengaluru / Bangalore
                                                </span>
                                                <span class="flex items-center bg-slate-50 border border-slate-200 hover:bg-slate-100 hover:border-slate-300 transition-all duration-200 px-2.5 py-1.5 rounded-md">
                                                    <i class="fa fa-clock-o mr-1.5 text-gray-400"></i>
                                                    Full-time
                                                </span>
                                            </div>

                                            <div class="mb-4">
                                                <div class="flex flex-wrap gap-2">
                                                    <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">Java</span>
                                                    <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">JavaScript</span>
                                                    <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">Angular</span>
                                                    <span class="transition-all duration-200 hover:-translate-y-0.5 hover:shadow-sm px-2.5 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200 font-medium">Spring Boot</span>
                                                </div>
                                            </div>

                                            <p class="text-gray-600 text-sm leading-relaxed line-clamp-2">
                                                We are looking for a talented Java developer with good understanding of Object-Oriented Programming...
                                            </p>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                        <div class="flex items-center gap-3">
                                            <button class="bg-gradient-to-br from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-2.5 rounded-lg hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 text-sm font-semibold">
                                                <i class="fa fa-paper-plane mr-2"></i>
                                                Apply Now
                                            </button>
                                            <button class="border border-gray-300 text-gray-700 px-4 py-2.5 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 text-sm font-medium">
                                                <i class="fa fa-bookmark-o mr-1.5"></i>
                                                Save
                                            </button>
                                        </div>
                                        <div class="flex items-center gap-4 text-sm text-gray-400">
                                            <button class="hover:text-red-500 transition-colors p-1">
                                                <i class="fa fa-heart-o text-base"></i>
                                            </button>
                                            <span class="flex items-center bg-gray-50 px-2 py-1 rounded-md">
                                                <i class="fa fa-eye mr-1.5"></i>
                                                15
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </section>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">

                <!-- Top Hiring Companies -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Top Hiring Companies</h3>
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-700 rounded-lg flex items-center justify-center">
                            <i class="fa fa-building text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="space-y-4">
                        @if(isset($topRecruiters) && $topRecruiters->count() > 0)
                            @foreach($topRecruiters->take(4) as $company)
                                <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                    <div class="w-12 h-12 flex-shrink-0">
                                        @if($company->logo)
                                            <img src="{{url('storage/' . $company->logo)}}" alt="{{$company->name}}" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                                        @else
                                            @php
                                                $companyColors = [
                                                    'from-blue-500 to-blue-600',
                                                    'from-green-500 to-green-600',
                                                    'from-purple-500 to-purple-600',
                                                    'from-red-500 to-red-600',
                                                    'from-yellow-500 to-yellow-600',
                                                    'from-indigo-500 to-indigo-600',
                                                    'from-pink-500 to-pink-600',
                                                    'from-teal-500 to-teal-600'
                                                ];
                                                $colorIndex = crc32($company->name) % count($companyColors);
                                                $color = $companyColors[$colorIndex];
                                            @endphp
                                            <div class="w-12 h-12 bg-gradient-to-br {{$color}} rounded-lg flex items-center justify-center shadow-sm">
                                                <span class="text-white font-bold text-sm">{{strtoupper(substr($company->name, 0, 1))}}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-semibold text-gray-900 truncate">{{$company->name}}</p>
                                        <p class="text-xs text-blue-600 font-medium">{{$company->job_listings_count}} Jobs Available</p>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 flex-shrink-0">
                                    <img src="{{asset('images/logo/logo1.png')}}" alt="Microsoft" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Microsoft</p>
                                    <p class="text-xs text-blue-600 font-medium">45 Jobs Available</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 flex-shrink-0">
                                    <img src="{{asset('images/logo/logo3.png')}}" alt="Google" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Google</p>
                                    <p class="text-xs text-blue-600 font-medium">38 Jobs Available</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 flex-shrink-0">
                                    <img src="{{asset('images/logo/logo6.png')}}" alt="Amazon" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Amazon</p>
                                    <p class="text-xs text-blue-600 font-medium">52 Jobs Available</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                                <div class="w-12 h-12 flex-shrink-0">
                                    <img src="{{asset('images/logo/logo9.png')}}" alt="Apple" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-semibold text-gray-900 truncate">Apple</p>
                                    <p class="text-xs text-blue-600 font-medium">29 Jobs Available</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Training Institutes -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Training Institutes</h3>
                        <div class="w-8 h-8 bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center">
                            <i class="fa fa-graduation-cap text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="w-12 h-12 flex-shrink-0">
                                <img src="{{asset('images/logo/logo10.png')}}" alt="Tech Academy" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 truncate">Tech Academy</p>
                                <p class="text-xs text-red-600 font-medium">15 Courses</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="w-12 h-12 flex-shrink-0">
                                <img src="{{asset('images/logo/logo11.png')}}" alt="Skill Development Center" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 truncate">Skill Development Center</p>
                                <p class="text-xs text-red-600 font-medium">22 Courses</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <div class="w-12 h-12 flex-shrink-0">
                                <img src="{{asset('images/logo/logo12.jpg')}}" alt="Professional Institute" class="w-12 h-12 object-contain rounded-lg border border-gray-200 bg-white">
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-semibold text-gray-900 truncate">Professional Institute</p>
                                <p class="text-xs text-red-600 font-medium">18 Courses</p>
                            </div>
                        </div>
                        <div class="pt-4 border-t border-gray-100">
                            <span class="block text-center text-gray-400 font-medium text-sm">
                                Coming Soon
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

@endsection