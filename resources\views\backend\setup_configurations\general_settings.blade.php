@extends('backend.layouts.app')

@section('content')
<div class="max-w-4xl mx-auto py-8 px-4">
    <div class="bg-white rounded-lg shadow-md">
        <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg">
            <h3 class="text-lg font-semibold">General Settings</h3>
        </div>
        <div class="p-6">
            <form action="{{ route('business_settings.update') }}" method="POST" class="space-y-6">
                @csrf
                <div>
                    <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">Site Name</label>
                    <input type="text" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200" id="site_name" name="site_name" value="{{ get_setting('site_name') }}">
                </div>
                <div>
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                    <textarea class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200" id="site_description" name="site_description" rows="3">{{ get_setting('site_description') }}</textarea>
                </div>
                <div>
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                    <input type="email" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200" id="contact_email" name="contact_email" value="{{ get_setting('contact_email') }}">
                </div>
                <div>
                    <label for="contact_phone" class="block text-sm font-medium text-gray-700 mb-2">Contact Phone</label>
                    <input type="text" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200" id="contact_phone" name="contact_phone" value="{{ get_setting('contact_phone') }}">
                </div>
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <textarea class="w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200" id="address" name="address" rows="3">{{ get_setting('address') }}</textarea>
                </div>
                <div class="pt-4">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection 