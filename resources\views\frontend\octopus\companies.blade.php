@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">Companies</h1>
                <p class="text-white link-nav">
                    <a href="{{url('/')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>Companies</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start Companies Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <h2 class="text-center mb-4">Top Companies</h2>
                
                @if($companies->count() > 0)
                    <div class="row">
                        @foreach($companies as $company)
                            <div class="col-lg-6 col-md-6 mb-4">
                                <div class="single-post d-flex flex-row">
                                    <div class="details col-lg-12">
                                        <div class="title d-flex flex-row justify-content-between">
                                            <div class="titles">
                                                <a href="{{ route('frontend.company', $company->slug) }}">
                                                    <h4>{{ $company->name }}</h4>
                                                </a>
                                                @if($company->location)
                                                    <h6><span class="lnr lnr-map"></span> {{ $company->location }}</h6>
                                                @endif
                                                <p>{{ $company->job_listings_count }} {{ Str::plural('Job', $company->job_listings_count) }} Available</p>
                                            </div>
                                            <div class="btns">
                                                <a href="{{ route('frontend.company', $company->slug) }}" class="btn btn-primary">View Profile</a>
                                            </div>
                                        </div>
                                        
                                        @if($company->description)
                                            <p>{{ Str::limit($company->description, 150) }}</p>
                                        @endif
                                        
                                        @if($company->industry)
                                            <h5>Industry: <span>{{ $company->industry }}</span></h5>
                                        @endif
                                        
                                        @if($company->website)
                                            <p class="address">
                                                <span class="lnr lnr-link"></span> 
                                                <a href="{{ $company->website }}" target="_blank">{{ $company->website }}</a>
                                            </p>
                                        @endif
                                        
                                        <div class="mt-2">
                                            <a href="{{ route('frontend.company.jobs', $company->slug) }}" class="btn btn-outline-primary btn-sm">
                                                View Jobs ({{ $company->job_listings_count }})
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            {{ $companies->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No companies found</h4>
                        <p>Please check back later for new companies.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End Companies Area -->
@endsection
