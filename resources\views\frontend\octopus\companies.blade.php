@extends('layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">Companies</h1>
                <p class="text-white link-nav">
                    <a href="{{url('')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>Companies</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start post Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row justify-content-center d-flex">
            <div class="col-lg-8 post-list">
                @if($companies->count() > 0)
                    @foreach($companies as $company)
                        <div class="single-post d-flex flex-row">
                            <div class="thumb">
                                <img src="{{url('theme/web/img/post.png')}}" alt="">
                                <ul class="tags">
                                    @if($company->industry)
                                        <li><a href="#">{{ $company->industry }}</a></li>
                                    @endif
                                </ul>
                            </div>
                            <div class="details">
                                <div class="title d-flex flex-row justify-content-between">
                                    <div class="titles">
                                        <a href="{{ route('frontend.company', $company->slug) }}">
                                            <h4>{{ $company->name }}</h4>
                                        </a>
                                        @if($company->location)
                                            <h6>{{ $company->location }}</h6>
                                        @endif
                                    </div>
                                    <ul class="btns">
                                        <li><a href="#"><span class="lnr lnr-heart"></span></a></li>
                                        <li><a href="{{ route('frontend.company', $company->slug) }}">View Profile</a></li>
                                    </ul>
                                </div>
                                @if($company->description)
                                    <p>{{ Str::limit($company->description, 200) }}</p>
                                @endif
                                <h5>Jobs Available: {{ $company->job_listings_count }}</h5>
                                @if($company->location)
                                    <p class="address"><span class="lnr lnr-map"></span> {{ $company->location }}</p>
                                @endif
                                @if($company->website)
                                    <p class="address"><span class="lnr lnr-link"></span> <a href="{{ $company->website }}" target="_blank">{{ $company->website }}</a></p>
                                @endif
                            </div>
                        </div>
                    @endforeach

                    <!-- Pagination -->
                    <div class="row justify-content-center">
                        <div class="col-lg-12">
                            {{ $companies->links() }}
                        </div>
                    </div>
                @else
                    <div class="text-center">
                        <h4>No companies found</h4>
                        <p>Please check back later for new companies.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
<!-- End post Area -->
@endsection
