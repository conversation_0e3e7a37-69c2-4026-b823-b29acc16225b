@extends('frontend.layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">Apply for {{ $jobListing->title }}</h1>

            <!-- Job Details Summary -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-2">Job Details</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-600">Company</p>
                        <p class="font-medium">{{ $jobListing->company->name }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Location</p>
                        <p class="font-medium">{{ $jobListing->location }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Job Type</p>
                        <p class="font-medium">{{ ucfirst($jobListing->job_type) }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Salary Range</p>
                        <p class="font-medium">
                            @if($jobListing->salary_min && $jobListing->salary_max)
                                {{ format_currency($jobListing->salary_min, $jobListing->salary_currency) }} -
                                {{ format_currency($jobListing->salary_max, $jobListing->salary_currency) }}
                            @else
                                Not specified
                            @endif
                        </p>
                    </div>
                </div>
            </div>

            <!-- Application Form -->
            <form action="{{ route('applications.store', $jobListing) }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="space-y-6">
                    <!-- Cover Letter -->
                    <div>
                        <label for="cover_letter" class="block text-sm font-medium text-gray-700 mb-2">
                            Cover Letter <span class="text-red-500">*</span>
                        </label>
                        <textarea
                            name="cover_letter"
                            id="cover_letter"
                            rows="6"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Tell us why you're the perfect fit for this role..."
                            required
                        >{{ old('cover_letter') }}</textarea>
                        @error('cover_letter')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Resume Upload -->
                    <div>
                        <label for="resume" class="block text-sm font-medium text-gray-700 mb-2">
                            Resume <span class="text-red-500">*</span>
                        </label>
                        <input
                            type="file"
                            name="resume"
                            id="resume"
                            accept=".pdf,.doc,.docx"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            required
                        >
                        <p class="mt-1 text-sm text-gray-500">Accepted formats: PDF, DOC, DOCX (Max: 5MB)</p>
                        @error('resume')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Additional Documents -->
                    <div>
                        <label for="additional_documents" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional Documents (Optional)
                        </label>
                        <input
                            type="file"
                            name="additional_documents[]"
                            id="additional_documents"
                            multiple
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                        <p class="mt-1 text-sm text-gray-500">Portfolio, certificates, etc. (Max: 5MB each)</p>
                        @error('additional_documents.*')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Application Questions (if any) -->
                    @if($jobListing->application_questions)
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Questions</h3>
                            @foreach($jobListing->application_questions as $index => $question)
                                <div class="mb-4">
                                    <label for="answers_{{ $index }}" class="block text-sm font-medium text-gray-700 mb-2">
                                        {{ $question }}
                                    </label>
                                    <textarea
                                        name="answers[{{ $index }}]"
                                        id="answers_{{ $index }}"
                                        rows="3"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Your answer..."
                                    >{{ old("answers.{$index}") }}</textarea>
                                </div>
                            @endforeach
                        </div>
                    @endif

                    <!-- Terms and Conditions -->
                    <div class="flex items-start">
                        <input
                            type="checkbox"
                            name="terms"
                            id="terms"
                            class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            required
                        >
                        <label for="terms" class="ml-2 text-sm text-gray-700">
                            I agree to the <a href="#" class="text-blue-600 hover:underline">Terms and Conditions</a>
                            and <a href="#" class="text-blue-600 hover:underline">Privacy Policy</a>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-between items-center pt-6 border-t">
                        <a href="{{ route('jobs-listing.show', $jobListing) }}"
                           class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition duration-200">
                            Back to Job
                        </a>
                        <button
                            type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition duration-200"
                        >
                            Submit Application
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
