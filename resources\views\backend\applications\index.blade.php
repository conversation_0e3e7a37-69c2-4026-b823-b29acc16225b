<x-app-backend-layout>

    <div class="max-w-7xl mx-auto px-4">
        <h1 class="text-2xl font-bold mb-4">Job Applications</h1>

        @if($applications->count())
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b">Applicant</th>
                        <th class="py-2 px-4 border-b">Job Listing</th>
                        <th class="py-2 px-4 border-b">Status</th>
                        <th class="py-2 px-4 border-b">Applied At</th>
                        <th class="py-2 px-4 border-b">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($applications as $application)
                    <tr>
                        <td class="py-2 px-4 border-b">{{ $application->user->name }}</td>
                        <td class="py-2 px-4 border-b">{{ $application->jobListing->title }}</td>
                        <td class="py-2 px-4 border-b">{{ ucfirst($application->status) }}</td>
                        <td class="py-2 px-4 border-b">{{ $application->created_at->format('Y-m-d') }}</td>
                        <td class="py-2 px-4 border-b">
                            <a href="{{ route('applications.show', $application) }}" class="text-blue-600 hover:underline">View</a>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <div class="mt-4">
                {{ $applications->links() }}
            </div>
        @else
            <p>No applications found.</p>
        @endif
    </div>
</x-app-backend-layout>
