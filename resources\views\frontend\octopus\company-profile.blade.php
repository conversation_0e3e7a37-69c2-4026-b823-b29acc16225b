@extends('frontend.octopus.layouts.master')

@section('content')
<!-- start banner Area -->
<section class="banner-area1 relative" id="home">
    <div class="overlay1 overlay-bg1"></div>
    <div class="container">
        <div class="row d-flex align-items-center justify-content-center">
            <div class="about-content col-lg-12">
                <h1 class="text-white">{{ $company->name }}</h1>
                <p class="text-white link-nav">
                    <a href="{{url('/')}}">Home</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <a href="{{url('/companies')}}">Companies</a>
                    <span class="lnr lnr-arrow-right"></span>
                    <span>{{ $company->name }}</span>
                </p>
            </div>
        </div>
    </div>
</section>
<!-- End banner Area -->

<!-- Start Company Profile Area -->
<section class="post-area section-gap">
    <div class="container">
        <div class="row">
            <!-- Company Details -->
            <div class="col-lg-8">
                <div class="single-post">
                    <div class="details">
                        <div class="title">
                            <h2>{{ $company->name }}</h2>
                            @if($company->industry)
                                <h5>Industry: <span>{{ $company->industry }}</span></h5>
                            @endif
                        </div>
                        
                        @if($company->description)
                            <div class="mt-4">
                                <h4>About {{ $company->name }}</h4>
                                <p>{{ $company->description }}</p>
                            </div>
                        @endif
                        
                        <div class="mt-4">
                            <h4>Company Information</h4>
                            <div class="row">
                                @if($company->location)
                                    <div class="col-md-6">
                                        <p><strong>Location:</strong> {{ $company->location }}</p>
                                    </div>
                                @endif
                                @if($company->website)
                                    <div class="col-md-6">
                                        <p><strong>Website:</strong> <a href="{{ $company->website }}" target="_blank">{{ $company->website }}</a></p>
                                    </div>
                                @endif
                                @if($company->email)
                                    <div class="col-md-6">
                                        <p><strong>Email:</strong> <a href="mailto:{{ $company->email }}">{{ $company->email }}</a></p>
                                    </div>
                                @endif
                                @if($company->phone)
                                    <div class="col-md-6">
                                        <p><strong>Phone:</strong> {{ $company->phone }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        @if($recentJobs->count() > 0)
                            <div class="mt-4">
                                <h4>Recent Job Openings</h4>
                                @foreach($recentJobs as $job)
                                    <div class="single-post d-flex flex-row mb-3">
                                        <div class="details col-lg-12">
                                            <div class="title d-flex flex-row justify-content-between">
                                                <div class="titles">
                                                    <a href="{{ route('frontend.job.show', $job->slug) }}">
                                                        <h5>{{ $job->title }}</h5>
                                                    </a>
                                                    <p>{{ $job->location }}</p>
                                                </div>
                                                <div class="btns">
                                                    <a href="{{ route('frontend.job.show', $job->slug) }}" class="btn btn-primary btn-sm">View Details</a>
                                                </div>
                                            </div>
                                            <p>{{ Str::limit($job->description, 150) }}</p>
                                            <p class="address">
                                                <span class="lnr lnr-database"></span> {{ $job->created_at->diffForHumans() }}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                                
                                @if($totalJobs > $recentJobs->count())
                                    <div class="text-center">
                                        <a href="{{ route('frontend.company.jobs', $company->slug) }}" class="btn btn-primary">
                                            View All Jobs ({{ $totalJobs }})
                                        </a>
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="single-post">
                    <div class="details">
                        <h4>Quick Actions</h4>
                        <div class="mt-3">
                            <a href="{{ route('frontend.company.jobs', $company->slug) }}" class="btn btn-primary btn-block mb-2">
                                View All Jobs ({{ $totalJobs }})
                            </a>
                            @if($company->website)
                                <a href="{{ $company->website }}" target="_blank" class="btn btn-outline-primary btn-block mb-2">
                                    Visit Website
                                </a>
                            @endif
                            @if($company->email)
                                <a href="mailto:{{ $company->email }}" class="btn btn-outline-primary btn-block">
                                    Contact Company
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- End Company Profile Area -->
@endsection
